const pricingHistoryList = require('../../db/pricings/history.json');

const TOTAL_ITEMS = 100;
const INITIAL_LIMIT = 10;

const pricing = {
	createdAt: '2023-06-10T00:55:49.139Z',
	hist: pricingHistoryList,
	updatedBy: '2023-06-10T00:55:49.139Z',
};

const items = Array(TOTAL_ITEMS).fill(pricing);

module.exports = (page = 0, limit = 10) => {
	const pageSize = Number(limit ?? INITIAL_LIMIT);
	page = Number(page);
	limit = Number(limit);

	return {
		data: {
			content: items.slice(page * limit, limit * (page + 1)),
			pageable: {
				sort: null,
				pageNumber: Number(page),
				pageSize,
				totalPages: Math.round(TOTAL_ITEMS / pageSize),
				totalElements: TOTAL_ITEMS,
			},
		},
		errors: [],
		meta: [],
	};
};
