const TOTAL_ITEMS = 100;
const INITIAL_LIMIT = 10;

const pricingHistoryList = [
	{
		startDate: '2023-05-25',
		endDate: '2023-05-29',
		tax: 41500,
		number: 3,
	},
	{
		startDate: '2023-03-15',
		endDate: '2023-03-20',
		tax: 0.98,
		number: 8,
	},
	{
		startDate: '2023-04-25',
		endDate: '2023-05-05',
		tax: 17500,
		number: 6,
	},
	{
		startDate: '2023-06-01',
		endDate: '2023-06-12',
		tax: 352000,
		number: 2,
	},
	{
		startDate: '2023-07-14',
		endDate: '2023-07-21',
		tax: 46800,
		number: 9,
	},
	{
		startDate: '2023-09-01',
		endDate: '2023-09-05',
		tax: 0.78,
		number: 4,
	},
	{
		startDate: '2023-10-10',
		endDate: '2023-10-20',
		tax: 39200,
		number: 7,
	},
	{
		startDate: '2023-11-16',
		endDate: '2023-11-30',
		tax: 12300,
		number: 1,
	},
	{
		startDate: '2024-01-02',
		endDate: '2024-01-10',
		tax: 47500,
		number: 10,
	},
];

const pricing = {
	createdAt: '2023-06-10T00:55:49.139Z',
	hist: pricingHistoryList,
	updatedBy: '2023-06-10T00:55:49.139Z',
};

const items = Array(TOTAL_ITEMS).fill(pricing);

module.exports = (page = 0, limit = 10) => {
	const pageSize = Number(limit ?? INITIAL_LIMIT);

	return {
		data: {
			content: items.slice(page * limit, limit * (page + 1)),
			pageable: {
				sort: null,
				pageNumber: Number(page),
				pageSize,
				totalPages: TOTAL_ITEMS / pageSize,
				totalElements: TOTAL_ITEMS,
			},
		},
		errors: [],
		meta: [],
	};
};
