import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { builderCommercialProductLimit } from '@/features/commercialProducts/helpers/builderCommercialProductLimit';
import { getFilterCommercialProductLimits } from '@/features/commercialProducts/services';

type UseFilterCommercialProductLimit = {
	commercialProductLimit: Ref<Array<any>>;
	isLoadingCommercialProductLimit: ComputedRef<boolean>;
	isErrorCommercialProductLimit: ComputedRef<boolean>;
	getCommercialProductLimit: Function;
};

export function useFilterCommercialProductLimit(): UseFilterCommercialProductLimit {
	const commercialProductLimit = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getFilterCommercialProductLimits(),
		onSuccess: (response) => {
			const data = builderCommercialProductLimit(response);
			commercialProductLimit.value = data;
		},
		onError: () => {
			commercialProductLimit.value = [];
		},
	});

	const isLoadingCommercialProductLimit = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProductLimit = computed(() => {
		return isError.value;
	});

	function getCommercialProductLimit() {
		mutate();
	}

	return {
		commercialProductLimit,
		isLoadingCommercialProductLimit,
		isErrorCommercialProductLimit,
		getCommercialProductLimit,
	};
}
