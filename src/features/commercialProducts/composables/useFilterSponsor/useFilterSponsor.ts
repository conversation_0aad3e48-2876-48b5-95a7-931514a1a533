import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { builderSponsor } from '@/features/commercialProducts/helpers/builderSponsor';
import { getFilterSponsor } from '@/features/commercialProducts/services';

type UseFilterSponsor = {
	sponsors: Ref<Array<any>>;
	isLoadingSponsor: ComputedRef<boolean>;
	isErrorSponsor: ComputedRef<boolean>;
	getSponsor: Function;
};

export function useFilterSponsor(): UseFilterSponsor {
	const sponsors = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getFilterSponsor(),
		onSuccess: (response) => {
			const data = builderSponsor(response);
			sponsors.value = data;
		},
		onError: () => {
			sponsors.value = [];
		},
	});

	const isLoadingSponsor = computed(() => {
		return isLoading.value;
	});

	const isErrorSponsor = computed(() => {
		return isError.value;
	});

	function getSponsor() {
		mutate();
	}

	return {
		sponsors,
		isLoadingSponsor,
		isErrorSponsor,
		getSponsor,
	};
}
