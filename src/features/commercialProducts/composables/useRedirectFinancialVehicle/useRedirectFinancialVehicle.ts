import { useRouter } from '@/composibles/useRouter';

type UseRedirectFinancialVehicle = {
	redirectNewAssociateFinancialVehicle: Function;
	redirectFinancialVehicle: Function;
};

export function useRedirectFinancialVehicle(): UseRedirectFinancialVehicle {
	const router = useRouter();

	function redirectNewAssociateFinancialVehicle(id): void {
		router.push(`/admin/cadastros/produto_comercial/${id}/associacao_veiculo_financeiro`);
	}

	function redirectFinancialVehicle(id): void {
		router.push(`/admin/cadastros/produto_comercial/${id}/editar?path=veiculos_financeiro`);
	}

	return {
		redirectNewAssociateFinancialVehicle,
		redirectFinancialVehicle
	};
}
