import { useRouter } from '@/composibles/useRouter';

type UseRedirectCommercialProduct = {
	redirectNewCommercialProduct: Function;
	redirectEditCommercialProduct: Function;
	redirectHomeCommercialProduct: Function;
	redirectEditForceCommercialProduct: Function;
	redirectEditForceMobility: Function;
};

export function useRedirectCommercialProduct(): UseRedirectCommercialProduct {
	const router = useRouter();

	function redirectNewCommercialProduct(): void {
		router.push(`/admin/cadastros/produto_comercial/novo`);
	}

	function redirectEditCommercialProduct(id): void {
		router.push(`/admin/cadastros/produto_comercial/${id}/editar`);
	}

	function redirectEditForceCommercialProduct(id): void {
		router.push(`/admin/cadastros/produto_comercial/${id}/editar`);
		router.go(0);
	}

	function redirectEditForceMobility(id): void {
		router.push(`/admin/cadastros/produto_comercial/${id}/editar?path=modalidades`);
		router.go(0);
	}

	function redirectHomeCommercialProduct(): void {
		router.push(`/admin/cadastros/produto_comercial`);
	}

	return {
		redirectNewCommercialProduct,
		redirectEditCommercialProduct,
		redirectHomeCommercialProduct,
		redirectEditForceCommercialProduct,
		redirectEditForceMobility
	};
}
