import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { builderCommercialProductById } from '@/features/commercialProducts/helpers/builderCommercialProductById';
import { getCommercialProductById as getCommercialProductByIdService } from '@/features/commercialProducts/services';
import { AxiosResponse } from 'axios';

type UseCommercialProductById = {
	commercialProduct: Ref<any>;
	isLoadingCommercialProductById: ComputedRef<boolean>;
	isErrorCommercialProductById: ComputedRef<boolean>;
	getCommercialProductById: Function;
};

export function useCommercialProductById(): UseCommercialProductById {
	const commercialProduct = ref({});
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getCommercialProductByIdService(params),
		onSuccess: async response => {
			const data = await builderCommercialProductById(response as AxiosResponse<any>);
			commercialProduct.value = data;
			if (callFunc) callFunc(false, data);
		},
		onError: () => {
			commercialProduct.value = null;
			if (callFunc) callFunc(true, null);
		},
	});

	const isLoadingCommercialProductById = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProductById = computed(() => {
		return isError.value;
	});

	function getCommercialProductById(data, callback: Function) {
		mutate(data);
		callFunc = callback;
	}

	return {
		commercialProduct,
		isLoadingCommercialProductById,
		isErrorCommercialProductById,
		getCommercialProductById,
	};
}
