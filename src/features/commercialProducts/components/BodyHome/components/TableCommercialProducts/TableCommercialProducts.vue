<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				id="table-commercial-products"
				item-key="key"
				:class="{
					'elevation-0': true,
					'ml-2': true,
					'table-commercial-products': true,
					'no-border': data.length === 0
				}"
				:items="data"
				:headers="headers"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader"
				:header-props="headerProps"
			>
				<template slot="no-data">
					<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
				</template>
				<template v-slot:header="{ props }" v-if="showCustomHeader">
					<farm-datatable-header
						:headers="props.headers"
						:sortClick="sortClicked"
						:selectedIndex="1"
						@onClickSort="onSort"
					/>
				</template>
				<template v-slot:[`item.status`]="{ item }">
					<status-active-and-inactive :status="item.status" uppercase />
				</template>
				<template v-slot:[`item.infos`]="{ item }">
					<farm-context-menu
						:items="onContextMenuItems(item)"
						@edit="onEditItem(item)"
					/>
				</template>
				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="filterCurrent.page +1"
						:totalPages="pagination.totalPages"
						:initialLimitPerPage="filterCurrent.limit"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangeLimitPerPage"
					/>
				</template>
			</v-data-table>
		</farm-box>
	</farm-row>
</template>

<script lang="ts">
import { computed, defineComponent, getCurrentInstance, ref, toRefs } from 'vue';
import { edit as editOption } from '@farm-investimentos/front-mfe-libs-ts';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';
import { useRedirectCommercialProduct } from '@/features/commercialProducts/composables/useRedirectCommercialProduct';

import { headers } from './configurations';

export default defineComponent({
	name: 'table-commercial-products',
	props: {
		data: {
			type: Array,
			required: true,
		},
		pagination: {
			type: Object,
			required: true,
		},
		filterCurrent: {
			type: Object,
			required: true,
		},
	},
	components:{
		StatusActiveAndInactive
	},
	setup(props, { emit }) {
		const { filterCurrent } = toRefs(props);

		const { redirectEditCommercialProduct } = useRedirectCommercialProduct();

		const internalInstance: any = getCurrentInstance();

		const showCustomHeader = computed<boolean>(() => {
			return internalInstance.proxy.$vuetify.breakpoint.name !== 'xs';
		});

		const sortClicked = ref([]);
		const currentPage = ref(1);
		const headerProps = ref({
			sortByText: 'Ordenar por',
		});

		function onContextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			return [editOption];
		}

		function onEditItem(item): void {
			redirectEditCommercialProduct(item.id);
		}

		function onChangePage(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			currentPage.value = pageActive + 1;
			emit('onRequest', { page: pageActive, limit: filterCurrent.value.limit });
		}

		function onChangeLimitPerPage(limit: number): void {
			currentPage.value = 1;
			emit('onRequest', { page: 0, limit: limit });
		}

		function onSort(data): void {
			const parseOrder ={
				id: 'id',
				name: 'name',
				status: 'enabled',
				dateEnd: 'endAt',
				type: 'typeId',
				dateStart:'startAt',
				limit: 'limitId'
			};
			const requestData = {
				order: data.descending,
				orderby: parseOrder[data.field],
				page: filterCurrent.value.page,
				limit: filterCurrent.value.limit
			};
			emit('onRequest', requestData);
		}

		return {
			currentPage,
			showCustomHeader,
			headerProps,
			sortClicked,
			headers,
			onContextMenuItems,
			onEditItem,
			onChangePage,
			onChangeLimitPerPage,
			onSort,
		};
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-commercial-products', 1, (0));
.table-commercial-products.no-border .v-data-table__wrapper {
	border-bottom: 0;
}
</style>
<style lang="scss" scoped>
@import './TableCommercialProducts';
</style>
