import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import {
	builderCommercialProducts,
	CommercialProductsData,
} from '@/features/commercialProducts/helpers/builderCommercialProducts';
import { getCommercialProducts as getCommercialProductsService } from '@/features/commercialProducts/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';
import { AxiosResponse } from 'axios';

type UseCommercialProducts = {
	commercialProducts: Ref<Array<any>>;
	commercialProductsPagination: Ref<any>;
	isLoadingCommercialProducts: ComputedRef<boolean>;
	isErrorCommercialProducts: ComputedRef<boolean>;
	getCommercialProducts: Function;
};

export function useCommercialProducts(): UseCommercialProducts {
	const commercialProducts = ref([]);
	const commercialProductsPagination = ref({});

	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getCommercialProductsService(params),
		onSuccess: async (response: AxiosResponse<CommercialProductsData>) => {
			const { content, pagination } = await builderCommercialProducts(response);
			commercialProducts.value = content;
			commercialProductsPagination.value = pagination;
			if (callFunc) callFunc(false);
		},
		onError: error => {
			if (isHttpRequestError(error, 404)) {
				commercialProducts.value = [];
				if (callFunc) callFunc(false);
				return;
			}
			commercialProducts.value = [];
			commercialProductsPagination.value = null;
			if (callFunc) callFunc(true);
		},
	});

	const isLoadingCommercialProducts = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProducts = computed(() => {
		return isError.value;
	});

	function getCommercialProducts(filters, callback: Function) {
		mutate(filters);
		callFunc = callback;
	}

	return {
		commercialProducts,
		commercialProductsPagination,
		isLoadingCommercialProducts,
		isErrorCommercialProducts,
		getCommercialProducts,
	};
}
