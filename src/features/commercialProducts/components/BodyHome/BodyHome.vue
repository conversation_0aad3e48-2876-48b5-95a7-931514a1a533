<template>
	<farm-container>
		<filters-and-button-new
			:isOpenFilter="isOpenFilter"
			:isFilterCounter="isFilterCounter"
			:pagination="commercialProductsPagination"
			@onFilter="onFilter"
			@onFilterClicked="onFilterClicked"
			@onClickMainFilter="onClickMainFilter"
			@onInputChangeMainFilter="onInputChangeMainFilter"
		/>
		<table-commercial-products
			v-if="!isError"
			class="mt-6"
			:data="commercialProducts"
			:pagination="commercialProductsPagination"
			:filterCurrent="filterCurrent"
			@onRequest="onRequest"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-4 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted, ref } from 'vue';

import { usePageable } from '@/composibles/usePageable';

import FiltersAndButtonNew from './components/FiltersAndButtonNew';
import TableCommercialProducts from './components/TableCommercialProducts';
import { useCommercialProducts } from './composibles/useCommercialProducts';

export default defineComponent({
	name: 'body-home',
	components: {
		FiltersAndButtonNew,
		TableCommercialProducts
	},
	setup(){
		const {
			commercialProducts,
			commercialProductsPagination,
			getCommercialProducts,
			isLoadingCommercialProducts,
		} = useCommercialProducts();
		const {
			isOpenFilter,
			isFilterCounter,
			onClickMainFilter,
			onInputChangeMainFilter,
			onFiltersApplied,
		} = usePageable(
			{
				calbackFn: params => {
					filterCurrent.value = { ...params };
					getCommercialProducts(params, updatedPage);
				},
				filters: {},
				keyInputSearch: 'search',
				sort: {
					order: 'DESC',
					orderby: 'id',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			commercialProductsPagination.value
		);

		const filterCurrent = ref({
			page:0,
			limit:10,
			order: 'DESC',
			orderby: 'id',
		});
		const isError = ref(false);

		const isLoading = computed(() => {
			return (
				isLoadingCommercialProducts.value
			);
		});

		function onRequest(data): void {
			filterCurrent.value = {
				...filterCurrent.value,
				...data
			};
			getCommercialProducts(filterCurrent.value, updatedPage);
		}

		function onFilter(data): void{
			filterCurrent.value ={
				...filterCurrent.value,
				...data
			};
			getCommercialProducts(filterCurrent.value, updatedPage);
		}

		function onFilterClicked(value): void{
			onFiltersApplied(value);
			if(value === false){
				filterCurrent.value = {
					page: 0,
					limit: filterCurrent.value.limit,
					order: 'DESC',
					orderby: 'id',
				};
				getCommercialProducts(filterCurrent.value, updatedPage);
			}
		}

		function updatedPage(hasError): void{
			if(hasError){
				isError.value = true;
				return;
			}
			isError.value = false;
		}

		function onReload(): void{
			load();
		}

		function load(): void {
			const payload = {
				page:0,
				limit:10,
				order: 'DESC',
				orderby: 'id',
			};
			getCommercialProducts(payload, updatedPage);
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			commercialProducts,
			commercialProductsPagination,
			filterCurrent,
			isOpenFilter,
			isFilterCounter,
			onReload,
			onRequest,
			onFilter,
			onFilterClicked,
			onClickMainFilter,
			onInputChangeMainFilter
		};
	}
});
</script>
