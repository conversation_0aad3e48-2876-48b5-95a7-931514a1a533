<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" md="3" sm="3">
				<farm-label for="filter-status">Status</farm-label>
				<farm-select
					ref="status"
					id="filter-status"
					item-text="label"
					item-value="id"
					v-model="statusModel"
					:items="statusItems"
				/>
			</farm-col>
			<farm-col cols="12" md="3" sm="3">
				<farm-label for="filter-type">Tipo</farm-label>
				<farm-select
					ref="type"
					id="filter-type"
					item-text="label"
					item-value="id"
					v-model="typeModel"
					:items="financialVehicleTypes"
				/>
			</farm-col>
			<farm-col cols="12">
				<farm-btn
					class="mr-2"
					title="Aplicar Filtros"
					outlined
					@click="applyFilters"
				>
					Aplicar Filtros
				</farm-btn>
				<farm-btn
					plain
					title="Limpar Filtros"
					color="primary"
					@click="onClickClearFilter"
				>
					Limpar Filtros
				</farm-btn>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name:'financial-vehicle-filter-form',
	props:{
		financialVehicleTypes:{
			type:Array,
			required: true
		}
	},
	setup(_, { emit }){
		const statusModel = ref(null);
		const typeModel = ref(null);
		const statusItems = ref([
			{ id: 1, label: 'Ativo' },
			{ id: 0, label: 'Inativo' },
		]);

		function validDataFilter() {
			let enabled = null;
			if(statusModel.value !== null){
				enabled = statusModel.value? true: false;
			}
			return {
				enabled,
				type: typeModel.value
			};
		}

		function onClickClearFilter(): void {
			statusModel.value = null;
			typeModel.value = null;
			const payload = validDataFilter();
			emit('onFilter', payload);
			emit('onFilterClicked', false);
		}

		function applyFilters(): void {
			const payload = validDataFilter();
			emit('onFilter', payload);
			emit('onFilterClicked', true);
		}

		return{
			statusItems,
			statusModel,
			typeModel,
			onClickClearFilter,
			applyFilters
		};
	}
});
</script>
