<template>
	<farm-row>
		<farm-col>
			<tabs-form
				:tabList="tabsList"
				:valueDefault="valueDefault"
				@onUpdateCurrentTab="onUpdateCurrentTab"
			/>
			<tab-card
				v-if="isTabCard"
				:isEdit="isEdit"
				@onUpdatedFuncSave="onUpdatedFuncSave"
				@onUpdatedDisabledButton="onUpdatedDisabledButton"
				@onUpdatedFooter="onUpdatedFooter"
			/>
			<tab-data
				v-if="isTabData"
				:isEdit="isEdit"
				@onUpdatedFuncSave="onUpdatedFuncSave"
				@onUpdatedDisabledButton="onUpdatedDisabledButton"
			/>
			<TabMobility
				v-if="isTabMobility"
				@onUpdatedFuncSave="onUpdatedFuncSave"
				@onUpdatedDisabledButton="onUpdatedDisabledButton"
			/>
			<TabFinancialVehicle v-if="isTabFinancialVehicle" />
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref, toRefs } from 'vue';

import TabsForm from '@/components/TabsForm';
import { DATA, FINANCIAL_VEHICLE, MOBLILITY, CARD } from '@/constants';

import TabData from '../TabData';
import TabMobility from '../TabMobility';
import TabFinancialVehicle from '../TabFinancialVehicle';
import TabCard from '../TabCard';
import { tabDefault, tabEdit } from './configurations';

export default defineComponent({
	name: 'tabs-content',
	components: {
		TabsForm,
		TabData,
		TabMobility,
		TabFinancialVehicle,
		TabCard,
	},
	props: {
		isEdit: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const { isEdit } = toRefs(props);

		const tabsList = ref([]);
		const currentTab = ref(DATA);
		const valueDefault = ref(DATA);

		const isTabData = computed(() => currentTab.value === DATA);
		const isTabMobility = computed(() => currentTab.value === MOBLILITY);
		const isTabFinancialVehicle = computed(() => currentTab.value === FINANCIAL_VEHICLE);
		const isTabCard = computed(() => currentTab.value === CARD);

		let currentActive = '';

		function updatedHiddenButtonSave(value): void {
			const isHiddenButton = value === FINANCIAL_VEHICLE;
			emit('onUpdatedHiddenButtonSave', isHiddenButton);
		}

		function onUpdateCurrentTab(value): void {
			if (currentActive !== value) {
				updatedHiddenButtonSave(value);
				currentActive = value;
			}
			currentTab.value = value;
		}

		function initValueTab(): void {
			if (isEdit.value) {
				tabsList.value = [...tabEdit];
				return;
			}
			tabsList.value = [...tabDefault];
		}

		function onUpdatedFuncSave(value): void {
			emit('onUpdatedFuncSave', value);
		}

		function onUpdatedDisabledButton(value): void {
			emit('onUpdatedDisabledButton', value);
		}

		function onUpdatedFooter(value): void {
			emit('onUpdatedFooter', value);
		}

		onMounted(() => {
			initValueTab();
		});

		return {
			isEdit,
			isTabData,
			isTabMobility,
			isTabFinancialVehicle,
			isTabCard,
			tabsList,
			currentTab,
			valueDefault,
			onUpdateCurrentTab,
			onUpdatedFuncSave,
			onUpdatedDisabledButton,
			onUpdatedFooter,
		};
	},
});
</script>
