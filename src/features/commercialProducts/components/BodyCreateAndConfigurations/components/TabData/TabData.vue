<template>
	<farm-box>
		<form-data
			v-if="!isError && commercialProductType && sponsors"
			:isEdit="isEdit"
			:commercialProductTypes="commercialProductType"
			:commercialProducSponsor="sponsors"
			:errorForm="errorForm"
			@onUpdatedDataForm="onUpdatedDataForm"
			@onUpdatedDisabledButton="onUpdatedDisabledButton"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, toRefs, ref } from 'vue';

import { useRouter } from '@/composibles';

import { useFilterCommercialProductType } from '@/features/commercialProducts/composables/useFilterCommercialProductType';
import { useFilterSponsor } from '@/features/commercialProducts/composables/useFilterSponsor';

import FormData from '../FormData';
import { useCreateCommercialProduct } from '../../composibles/useCreateCommercialProduct';
import { useEditCommercialProduct } from '../../composibles/useEditCommercialProduct';
import { useCommercialProductMessage } from '../../composibles/useCommercialProductMessage';

export default defineComponent({
	name: 'tab-data',
	components: {
		FormData,
	},
	props: {
		isEdit: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const { isEdit } = toRefs(props);

		const router = useRouter();
		const {
			commercialProductType,
			getCommercialProductType,
			isErrorCommercialProductType,
			isLoadingCommercialProductType,
		} = useFilterCommercialProductType();
		const { createCommercialProduct, isLoadingCreateCommercialProduct } =
			useCreateCommercialProduct();
		const {
			commercialProductConfirm,
			notificationErrorCommercialProduct,
			updatedCommercialProductConfirm,
		} = useCommercialProductMessage();
		const { editCommercialProduct, isLoadingEditCommercialProduct } =
			useEditCommercialProduct();
		const {
			sponsors,
			isLoadingSponsor,
			isErrorSponsor,
			getSponsor,
		} = useFilterSponsor();

		const isLoading = computed(() => {
			return (
				isLoadingCommercialProductType.value ||
				isLoadingCreateCommercialProduct.value ||
				isLoadingEditCommercialProduct.value ||
				isLoadingSponsor.value
			);
		});
		const isError = computed(() => {
			return isErrorCommercialProductType.value || isErrorSponsor.value;
		});

		const form = ref(null);

		const errorForm = ref<boolean | string>(true);

		function createPayload() {
			return {
				name: form.value.name.toUpperCase().trim(),
				typeId: parseInt(form.value.typeId, 10),
				accountProductId: form.value.sponsor || null,
				startAt: form.value.startDate,
				endAt: form.value.endDate,
			};
		}

		function updatedPageCreate(hasError, data): void {
			if (hasError) {
				if (data !== null) {
					errorForm.value = 'Produto Comercial já existente.';
					return;
				}
				notificationErrorCommercialProduct(false);
				return;
			}
			commercialProductConfirm(data.id);
		}

		function updatedPageEdit(hasError, data): void {
			if (hasError) {
				if (data !== null) {
					errorForm.value = 'Produto Comercial já existente.';
					return;
				}
				notificationErrorCommercialProduct(false);
				return;
			}
			updatedCommercialProductConfirm(data.id);
		}

		function edit(): void {
			const payload = {
				id: router.currentRoute.params.id,
				payload: {
					...createPayload(),
					enabled: form.value.enabled,
					configurations: form.value.configurations,
				},
			};
			editCommercialProduct(payload, updatedPageEdit);
		}

		function create(): void {
			const payload = createPayload();
			createCommercialProduct(payload, updatedPageCreate);
		}

		function onUpdatedDataForm(value): void {
			form.value = value;
		}

		function onUpdatedDisabledButton(value): void {
			emit('onUpdatedDisabledButton', value);
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			getCommercialProductType();
			getSponsor();
		}

		onMounted(() => {
			load();
			if (isEdit.value) {
				emit('onUpdatedFuncSave', edit);
				return;
			}
			emit('onUpdatedFuncSave', create);
		});

		return {
			isLoading,
			isError,
			errorForm,
			commercialProductType,
			sponsors,
			onReload,
			onUpdatedDataForm,
			onUpdatedDisabledButton,
		};
	},
});
</script>
