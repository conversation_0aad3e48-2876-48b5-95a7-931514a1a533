<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<provider-selected-card
					:data="item"
					:unauthorizedProviders="unauthorizedProviders"
					@add-to-array="addToList"
					@remove-from-array="removeFromList"
				/>
			</farm-col>
		</farm-row>
		<farm-row v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, toRefs, computed, watch } from 'vue';
import ProviderSelectedCard from '../ProviderSelectedCard';

export default defineComponent({
	name: 'ProviderSelectedList',
	components: {
		ProviderSelectedCard,
	},
	props: {
		data: {
			type: Array,
			required: false,
			default: () => [],
		},
		unauthorizedProviders: {
			type: Array,
			required: false,
			default: () => [],
		},
		listOfSelected: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	setup(props, { emit }) {
		const { data, unauthorizedProviders, listOfSelected } = toRefs(props);

		const isDataEmpty = computed(() => data.value.length === 0);

		const addToList = (id: number) => {
			if (!listOfSelected.value.includes(id)) {
				emit('add-to-list', id);
			}
		};

		const removeFromList = (id: number) => {
			emit('remove-from-list', id);
		};

		watch(
			() => unauthorizedProviders.value,
			() => {
				data.value.forEach(item => {
					if (!unauthorizedProviders.value.includes(item.id)) {
						emit('add-to-list', item.id);
					}
				});
			},
			{ immediate: true }
		);
		watch(
			() => props.unauthorizedProviders,
			() => {
				data.value.forEach(item => {
					if (props.unauthorizedProviders.includes(item.id)) {
						emit('remove-from-list', item.id);
					}
				});
			}
		);

		return {
			data,
			isDataEmpty,
			unauthorizedProviders,
			listOfSelected,
			addToList,
			removeFromList,
		};
	},
});
</script>

<style scoped>
.unauthorized-providers {
	margin-top: 1rem;
}
.unauthorized-providers ul {
	list-style-type: none;
	padding: 0;
	margin: 0;
}
.unauthorized-providers li {
	padding: 0.5rem 0;
	border-bottom: 1px solid #ddd;
}
</style>
