<template>
	<farm-box>
		<farm-row class="mb-6">
			<farm-col cols="12" md="6">
				<farm-idcaption icon="panorama-outline" copyText="">
					<template #title>
						<farm-heading v-if="!isPreview" :type="6">Adicionar Imagem</farm-heading>
						<farm-heading v-else :type="6">Visualizar ou Alterar Imagem</farm-heading>
					</template>
					<template #subtitle>
						<span v-if="!isPreview"
							>Faça o upload de uma imagem em formato <b>SVG</b> no tamanho máximo de
							<b>10mb</b>.
						</span>
						<span v-else>Visualize ou Altere a imagem do seu cartão atual.</span>
					</template>
				</farm-idcaption>
			</farm-col>
			<farm-col v-if="isPreview" class="d-flex align-center justify-end" cols="12" md="6">
				<farm-btn class="mr-2" outlined @click="alterImage"
					><farm-icon>swap-horizontal</farm-icon>Alterar Imagem</farm-btn
				>
				<farm-btn color="error" outlined @click="showDeleteImage = true"
					><farm-icon>delete-outline</farm-icon>Excluir Imagem</farm-btn
				>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isPreview" class="mb-6">
			<farm-col col="12">
				<farm-alertbox color="neutral" icon="alert-circle-outline" dismissable>
					Caso não seja feito o upload de uma imagem personalizada, utilizaremos uma
					imagem pré-definida por marketing com a identidade visual da Farmtech.
				</farm-alertbox>
			</farm-col>
		</farm-row>
		<farm-row v-if="imageError" class="mb-6">
			<farm-col col="12">
				<farm-alertbox color="warning" icon="alert-outline">
					O arquivo enviado {{ imageErrorName }} não pode ser enviado. Envie arquivos no
					formato: <b>SVG</b>
				</farm-alertbox>
			</farm-col>
		</farm-row>
		<farm-row v-if="isPreview" class="mb-6">
			<farm-col col="12">
				<card-preview
					v-if="cardPreview.image"
					:name="cardPreview.name"
					:image="cardPreview.image"
				/>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isPreview" class="mb-6">
			<farm-col col="12">
				<farm-multiple-filepicker
					:maxFileSize="10"
					:maxFilesNumber="1"
					acceptedFileTypes="image/svg+xml"
					@onMaxFileSizeWarning="showMaxSizeWarning"
					@onFileChange="updateFiles"
					@onInvalidFiles="onInvalidFiles"
				/>
			</farm-col>
		</farm-row>
		<farm-prompt-user
			match="EXCLUIR"
			title="Excluir Imagem"
			subtitle="<p>Deseja realmente excluir a imagem atual do cartão? Ela será removida da tela principal e substituída por uma imagem padrão.</p>
<p>Escreva no campo abaixo <b>EXCLUIR</b> para confirmar a remoção
da imagem.</p>"
			v-model="showDeleteImage"
			confirmLabel="Ok"
			@onConfirm="removeImage"
		/>
		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-box>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';
import { notification, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import { useRoute } from '@/composibles';
import { useAddCardImage } from '../../composibles/useAddCardImage';
import CardPreview from '../CardPreview';
import { useCommercialProductById } from '@/features/commercialProducts/composables/useCommercialProductById';
import { useRemoveCardImage } from '../../composibles/useRemoveCardImage';

export default defineComponent({
	name: 'tab-card',
	components: {
		CardPreview,
	},
	setup(_, { emit }) {
		const route = useRoute();
		const imageError = ref(false);
		const imageErrorName = ref('');
		const file = ref(null);
		const isPreview = ref(false);
		const showDeleteImage = ref(false);
		const isAlterImage = ref(false);
		const cardPreview = ref({ name: '', image: '', loaded: false });
		const { addCardImage, isLoadingAddCardImage } = useAddCardImage();
		const { removeCardImage, isLoadingRemoveCardImage } = useRemoveCardImage();
		const { commercialProduct, getCommercialProductById, isLoadingCommercialProductById } =
			useCommercialProductById();

		const showMaxSizeWarning = data => {
			imageErrorName.value = data[0].name;
			imageError.value = true;
			emit('onUpdatedDisabledButton', false);
		};

		const updateFiles = files => {
			if (files.length === 0) {
				emit('onUpdatedDisabledButton', false);

				return;
			}
			file.value = files[0];
			emit('onUpdatedDisabledButton', true);
		};

		const onInvalidFiles = data => {
			imageErrorName.value = data[0].name;
			imageError.value = true;
			emit('onUpdatedDisabledButton', false);
		};

		const mountCard = hasError => {
			if (hasError) {
				notification(RequestStatusEnum.ERROR, 'Ocorreu um erro ao enviar a imagem!');
				isAlterImage.value = false;
				return;
			}
			if (isAlterImage.value) {
				notification(RequestStatusEnum.SUCCESS, 'Imagem alterada com sucesso!');
			} else {
				notification(RequestStatusEnum.SUCCESS, 'Imagem enviada com sucesso!');
			}
			isAlterImage.value = false;
			emit('onUpdatedDisabledButton', false);
			isPreview.value = true;
			setTimeout(() => {
				getCommercialProductById({ id: route.params.id });
			}, 2000);
		};

		watch(commercialProduct, () => {
			isPreview.value = false;
			cardPreview.value.loaded = false;
			cardPreview.value.image = '';
			cardPreview.value.name = commercialProduct.value?.data.name;
			cardPreview.value.image = commercialProduct.value?.data.cardImgUrl;

			if (cardPreview.value.image) {
				isPreview.value = true;
				cardPreview.value.loaded = true;
			}

			emit('onUpdatedFooter', commercialProduct.value.meta);
		});

		const saveImage = async () => {
			const formData = new FormData();
			formData.append('file', file.value, file.value.name);
			addCardImage({ id: route.params.id, payload: formData }, mountCard);
		};

		const alterImage = () => {
			emit('onUpdatedDisabledButton', false);
			isPreview.value = false;
			isAlterImage.value = true;
		};

		const removeImage = () => {
			emit('onUpdatedDisabledButton', false);
			isPreview.value = false;
			removeCardImage({ id: route.params.id }, hasError => {
				if (!hasError) {
					notification(RequestStatusEnum.SUCCESS, 'Imagem excluida com sucesso!');
					getCommercialProductById({ id: route.params.id });

					return;
				}
				notification(
					RequestStatusEnum.ERROR,
					'Ocorreu um erro ao remover a imagem do cartão!'
				);
			});
		};

		const isLoading = computed(
			() =>
				isLoadingAddCardImage.value ||
				isLoadingCommercialProductById.value ||
				isLoadingRemoveCardImage.value
		);

		onMounted(() => {
			emit('onUpdatedFuncSave', saveImage);
			emit('onUpdatedDisabledButton', false);
			getCommercialProductById({ id: route.params.id });
		});

		return {
			showMaxSizeWarning,
			updateFiles,
			onInvalidFiles,
			imageError,
			imageErrorName,
			isLoading,
			isPreview,
			cardPreview,
			alterImage,
			showDeleteImage,
			removeImage,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './TabCard';
</style>
