<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
					<CardListTextHeader :data="data.listIdAndDocument" noSpacing class="mb-1" />
				</farm-col>
				<farm-col cols="3" align="end">
					<div class="d-flex justify-end align-center">
						<StatusActiveAndInactive :status="data.status" dense class="mr-2" />
						<farm-context-menu
							:items="contextMenuItems(data)"
							@finalizeAssociation="onFinalizeAssociation(data)"
							@reAssociate="onReAssociateOption(data)"
							@allowedSuppliers="onAllowedSuppliers(data)"
						/>
					</div>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="8">
					<CardTextBody ellipsisValue label="Tipo" :value="formatValueOrNA(data.type)" />
				</farm-col>
				<farm-col cols="4" v-if="data.endRelationship === null">
					<CardTextBody
						label="Associado em"
						:value="formatDateOrNA(data.startRelationship)"
					/>
				</farm-col>
				<farm-col cols="4" v-if="data.endRelationship !== null">
					<CardTextBody
						label="Finalizado em"
						:value="formatDateOrNA(data.endRelationship)"
					/>
				</farm-col>
				<farm-loader mode="overlay" v-if="isLoading" />
				<farm-prompt-user
					v-model="isShowModal"
					title="Finalizar Associação"
					subtitle=""
					match="REMOVER"
					@onConfirm="onConfirm"
					@onClose="onClose"
				>
					<template v-slot:subtitle>
						<farm-typography size="md" class="mt-6">
							Deseja realmente finalizar a associação com o veículo financeiro
						</farm-typography>
						<farm-typography bold size="md"> {{ dataSelected.name }}?</farm-typography>
						<farm-typography size="md" class="mt-3">
							A alteração refletirá em todos os parceiros associados a este Produto
							Comercial.
						</farm-typography>
						<farm-typography size="md" class="mt-3">
							Escreva no campo abaixo
							<farm-typography bold size="md" tag="span">“REMOVER”</farm-typography>
							para confirmar o fim da associação.
						</farm-typography>
					</template>
				</farm-prompt-user>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent, ref, getCurrentInstance, computed } from 'vue';

import { useRouter } from '@/composibles';
import Cards from '@/components/Cards';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import CardTextBody from '@/components/CardTextBody';
import CardContextMenu from '@/components/CardContextMenu';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';
import { formatValueOrNA, formatDateOrNA } from '@/helpers/formatCards';

import { useAssociationFinancialVehicle } from '../../composibles/useAssociationFinancialVehicle';
import { useFinancialVehicleRedirect } from '../../composibles/useFinancialVehicleRedirect';
import { useAssociationFinancialVehicleMessage } from '../../composibles/useAssociationFinancialVehicleMessage';

export default defineComponent({
	name: 'financial-vehicles-card',
	components: {
		Cards,
		CardTitleHeader,
		CardListTextHeader,
		CardTextBody,
		CardContextMenu,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	setup(_, { emit }) {
		const router = useRouter();
		const { associationFinancialVehicle, isLoadingAssociationFinancialVehicle } =
			useAssociationFinancialVehicle();
		const { forceRedirectFinancialVehicle } = useFinancialVehicleRedirect();
		const { confirmReAssociation } = useAssociationFinancialVehicleMessage();

		const isLoading = computed(() => {
			return isLoadingAssociationFinancialVehicle.value;
		});

		const internalInstance: any = getCurrentInstance().proxy;

		const terminateAssociationOption = ref({
			label: 'Finalizar associação',
			handler: 'finalizeAssociation',
			icon: { color: 'error', type: 'close-circle-outline' },
		});
		const reAssociate = ref({
			label: 'Associar Novamente',
			handler: 'reAssociate',
			icon: { color: 'primary', type: 'reload' },
		});
		const dataSelected = ref(null);
		const isShowModal = ref(false);

		function onClose(): void {
			isShowModal.value = false;
		}

		function onConfirm(): void {
			const id = router.currentRoute.params.id;
			const payload = {
				id,
				financialVehicleId: dataSelected.value.id,
				name: dataSelected.value.name,
				payload: {
					enabled: 0,
				},
			};
			associationFinancialVehicle(payload, updatedPage);
			isShowModal.value = false;
		}

		function contextMenuItems(data) {
			const allowedSuppliersOption = {
				label: 'Fornecedores Permitidos',
				handler: 'allowedSuppliers',
				icon: { color: 'primary', type: 'account-multiple' },
			};

			if (!internalInstance.canWrite) {
				return [];
			}

			if (data.endRelationship === null) {
				return [terminateAssociationOption.value, allowedSuppliersOption];
			} else {
				return [reAssociate.value, allowedSuppliersOption];
			}
		}

		function onAllowedSuppliers(data): void {
			emit('onAllowedSuppliers', data);
		}

		function updatedPage(): void {
			const id = router.currentRoute.params.id;
			forceRedirectFinancialVehicle(id);
		}

		function onFinalizeAssociation(data): void {
			dataSelected.value = data;
			isShowModal.value = true;
		}

		function callReAssociate(): void {
			const id = router.currentRoute.params.id;
			const payload = {
				id,
				financialVehicleId: dataSelected.value.id,
				payload: {
					enabled: 1,
				},
			};
			associationFinancialVehicle(payload, updatedPage);
		}

		function onReAssociateOption(data): void {
			dataSelected.value = data;
			confirmReAssociation(dataSelected.value.name, callReAssociate);
		}

		return {
			isLoading,
			isShowModal,
			dataSelected,
			formatValueOrNA,
			formatDateOrNA,
			contextMenuItems,
			onFinalizeAssociation,
			onAllowedSuppliers,
			onReAssociateOption,
			onClose,
			onConfirm,
		};
	},
});
</script>
