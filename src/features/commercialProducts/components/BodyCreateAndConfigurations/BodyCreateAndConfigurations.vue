<template>
	<farm-container>
		<informations-header v-if="isEdit" @onUpdatedFooter="onUpdatedFooter" />
		<farm-row>
			<farm-col cols="12">
				<tabs-content
					:isEdit="isEdit"
					@onUpdatedFuncSave="onUpdatedFuncSave"
					@onUpdatedDisabledButton="onUpdatedDisabledButton"
					@onUpdatedHiddenButtonSave="onUpdatedHiddenButtonSave"
					@onUpdatedFooter="onUpdatedFooter"
				/>
			</farm-col>
		</farm-row>
		<footer-form
			:data="dataFooterForm"
			:isDisabledButton="isDisabledButton"
			:labelButton="isEdit ? 'Salvar' : 'Cadastrar'"
			:showLayoutData="isEdit"
			:hiddenButton="hiddenButton"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs, ref } from 'vue';

import FooterForm from '@/components/FooterForm';
import { EDIT } from '@/constants';
import { useRedirectCommercialProduct } from '@/features/commercialProducts/composables/useRedirectCommercialProduct';

import TabsContent from './components/TabsContent';
import InformationsHeader from '../InformationsHeader';

export default defineComponent({
	name: 'body-create-and-configurations',
	components: {
		TabsContent,
		FooterForm,
		InformationsHeader,
	},
	props: {
		type: {
			type: String,
			require: true,
		},
	},
	setup(props) {
		const { type } = toRefs(props);

		const { redirectHomeCommercialProduct } = useRedirectCommercialProduct();

		const isEdit = computed(() => type.value === EDIT);

		const isDisabledButton = ref(true);
		const callFunction = ref(null);
		const dataFooterForm = ref(null);
		const hiddenButton = ref(false);

		function onUpdatedHiddenButtonSave(value): void {
			hiddenButton.value = value;
		}

		function onUpdatedDisabledButton(value): void {
			isDisabledButton.value = value;
		}

		function onUpdatedFuncSave(value): void {
			callFunction.value = value;
		}

		function onUpdatedFooter(value): void {
			dataFooterForm.value = value;
		}

		function onCancel(): void {
			redirectHomeCommercialProduct();
		}

		function onSave(): void {
			callFunction.value();
		}

		return {
			isEdit,
			isDisabledButton,
			dataFooterForm,
			hiddenButton,
			onCancel,
			onSave,
			onUpdatedDisabledButton,
			onUpdatedFuncSave,
			onUpdatedFooter,
			onUpdatedHiddenButtonSave,
		};
	},
});
</script>
