import { getCurrentInstance } from 'vue';

type UseAssociationFinancialVehicleMessage = {
	confirmReAssociation: Function;
};

export function useAssociationFinancialVehicleMessage(): UseAssociationFinancialVehicleMessage {

	const internalInstance = getCurrentInstance().proxy;

	function confirmReAssociation(name, callback) {
		const titleAndBody = {
			body: `Deseja associar novamente o Veículo Financeiro <b>${name}</b>?`,
			title: 'Associar Novamente',
		};
		const configurtations = {
			html: true,
			okText: 'Sim',
			cancelText: 'Cancelar',
		};
		internalInstance['$dialog'].confirm(titleAndBody, configurtations)
		.then(() => {
			callback();
		})
		.catch(() => {
		});
	}

	return {
		confirmReAssociation
	};
}



