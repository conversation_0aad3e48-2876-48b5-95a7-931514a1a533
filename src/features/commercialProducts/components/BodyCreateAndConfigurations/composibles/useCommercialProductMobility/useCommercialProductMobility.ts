import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { getCommercialProductMobility as getCommercialProductMobilityService } from '@/features/commercialProducts/services';
import { builderCommercialProductMobility } from '@/features/commercialProducts/helpers/builderCommercialProductMobility';

type UseCommercialProductMobility = {
	isLoadingCommercialProductMobility: ComputedRef<boolean>;
	isErrorCommercialProductMobility: ComputedRef<boolean>;
	getCommercialProductMobility: Function;
};

export function useCommercialProductMobility(): UseCommercialProductMobility {
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getCommercialProductMobilityService(params),
		onSuccess: (response) => {
			const data = builderCommercialProductMobility(response);
			if(callFunc) callFunc(false, data);
		},
		onError: () => {
			if(callFunc) callFunc(true, null);
		},
	});

	const isLoadingCommercialProductMobility = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProductMobility = computed(() => {
		return isError.value;
	});

	function getCommercialProductMobility(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
	}

	return {
		isLoadingCommercialProductMobility,
		isErrorCommercialProductMobility,
		getCommercialProductMobility,
	};
}
