import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { removeCardImage as removeCardImageService } from '@/features/commercialProducts/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseRemoveCardImage = {
	isLoadingRemoveCardImage: ComputedRef<boolean>;
	isErrorRemoveCardImage: ComputedRef<boolean>;
	removeCardImage: Function;
};

export function useRemoveCardImage(): UseRemoveCardImage {
	let payloadCache = null;
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => removeCardImageService(params),
		onSuccess: () => {
			if (callFunc) callFunc(false, payloadCache);
		},
		onError: error => {
			if (isHttpRequestError(error, 400)) {
				if (callFunc) callFunc(true, { duplicated: true });
				return;
			}
			if (callFunc) callFunc(true, null);
		},
	});

	const isLoadingRemoveCardImage = computed(() => {
		return isLoading.value;
	});

	const isErrorRemoveCardImage = computed(() => {
		return isError.value;
	});

	function removeCardImage(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
		payloadCache = payload;
	}

	return {
		isLoadingRemoveCardImage,
		isErrorRemoveCardImage,
		removeCardImage,
	};
}
