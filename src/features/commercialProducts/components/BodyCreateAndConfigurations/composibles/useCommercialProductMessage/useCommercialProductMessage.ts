import { getCurrentInstance } from 'vue';
import { notification, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { useRedirectCommercialProduct } from '@/features/commercialProducts/composables/useRedirectCommercialProduct';

type UseCommercialProductMessage = {
	commercialProductConfirm: Function;
	notificationErrorCommercialProduct: Function;
	updatedCommercialProductConfirm: Function;
	updatedStatus: Function;
	updatedMobilityCommercialProduct: Function;
	notificationSuccessMobility: Function;
};

export function useCommercialProductMessage(): UseCommercialProductMessage {

	const {
		redirectHomeCommercialProduct,
		redirectEditForceCommercialProduct
	} = useRedirectCommercialProduct();

	const internalInstance = getCurrentInstance().proxy;

	function commercialProductConfirm(id) {
		const titleAndBody = {
			body: `Produto Comercial cadastrado com sucesso. Deseja continuar para a edição? `,
			title: 'Sucesso',
		};
		const configurtations = {
			html: true,
			okText: 'Sim',
			cancelText: 'Cancelar',
		};
		internalInstance['$dialog'].confirm(titleAndBody, configurtations)
			.then(() => {
				redirectEditForceCommercialProduct(id);
			})
			.catch(() => {
				redirectHomeCommercialProduct();
			});
	}

	function updatedCommercialProductConfirm(id) {
		const titleAndBody = {
			body: `Dados do Produto Comercial atualizados com sucesso. Deseja continuar editando? `,
			title: 'Sucesso',
		};
		const configurtations = {
			html: true,
			okText: 'Sim',
			cancelText: 'Cancelar',
		};
		internalInstance['$dialog'].confirm(titleAndBody, configurtations)
			.then(() => {
				redirectEditForceCommercialProduct(id);
			})
			.catch(() => {
				redirectHomeCommercialProduct();
			});
	}

	function updatedMobilityCommercialProduct(callApis) {
		const titleAndBody = {
			body: `Tem certeza que deseja alterar as modalidades deste Produto Comercial?<br/>
				A alteração refletirá em todos os parceiros associados a este Produto Comercial.
				`,
			title: 'Alterar Modalidades',
		};
		const configurtations = {
			html: true,
			okText: 'Sim',
			cancelText: 'Cancelar',
		};
		internalInstance['$dialog'].confirm(titleAndBody, configurtations)
			.then(() => {
				callApis();
			})
			.catch(() => {
			});
	}

	function updatedStatus(onSucess, onError) {
		const titleAndBody = {
			body: `Inativar o Produto Comercial impactará diretamente em todos os parceiros associados. Deseja continuar?`,
			title: 'Inativar Produto Comercial',
		};
		const configurtations = {
			html: true,
			okText: 'Sim',
			cancelText: 'Cancelar',
		};
		internalInstance['$dialog'].confirm(titleAndBody, configurtations)
			.then(() => {
				onSucess();
			})
			.catch(() => {
				onError();
			});
	}

	function notificationErrorCommercialProduct(isEdit): void {
		const messageEdit = 'Erro ao editar o Produto Comercial por favor tente mais tarde.';
		const messageNew = 'Erro ao criar um novo Produto Comercial por favor tente mais tarde.';
		notification(
			RequestStatusEnum.ERROR,
			isEdit ? messageEdit : messageNew
		);
	}

	function notificationSuccessMobility(): void {
		const message = 'Modalidades atualizadas com sucesso!';
		notification(
			RequestStatusEnum.SUCCESS,
			message
		);
	}

	return {
		commercialProductConfirm,
		notificationErrorCommercialProduct,
		updatedCommercialProductConfirm,
		updatedStatus,
		updatedMobilityCommercialProduct,
		notificationSuccessMobility
	};
}



