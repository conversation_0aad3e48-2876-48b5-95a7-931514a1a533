import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import { associationFinancialVehicle as associationFinancialVehicleService } from '@/features/commercialProducts/services';

type UseAssociationFinancialVehicle = {
	isLoadingAssociationFinancialVehicle: ComputedRef<boolean>;
	isErrorAssociationFinancialVehicle: ComputedRef<boolean>;
	associationFinancialVehicle: Function;
};

export function useAssociationFinancialVehicle(): UseAssociationFinancialVehicle {
	let callFunc: Function | null = null;
	let payloadCache = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => associationFinancialVehicleService(params),
		onSuccess: () => {
			createNotification();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 2500);
			}
		},
		onError: () => {
			createNotificationError();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 2500);
			}
		},
	});

	function createNotification(): void {
		const messageFinish = `Associação finalizada com sucesso para o Veículo Financeiro ${payloadCache.name}`;
		const messageAssociation = `Veículo Financeiro associado com sucesso!`;
		notification(
			RequestStatusEnum.SUCCESS,
			payloadCache.isFinish ? messageFinish : messageAssociation
		);
	}

	function createNotificationError(): void {
		const message = payloadCache.isFinish ? `finalizar a associação do`: `associar o`;
		notification(
			RequestStatusEnum.ERROR,
			`Não foi possível ${message} Veículo Financeiro. Por favor tente mais tarde.`
		);
	}

	const isLoadingAssociationFinancialVehicle = computed(() => {
		return isLoading.value;
	});

	const isErrorAssociationFinancialVehicle = computed(() => {
		return isError.value;
	});

	function associationFinancialVehicle(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
		payloadCache = {
			name: payload.name,
			isFinish: payload.payload.enabled === 0
		};
	}

	return {
		isLoadingAssociationFinancialVehicle,
		isErrorAssociationFinancialVehicle,
		associationFinancialVehicle,
	};
}
