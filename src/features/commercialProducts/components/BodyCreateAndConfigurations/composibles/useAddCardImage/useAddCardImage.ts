import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { addCardImage as addCardImageService } from '@/features/commercialProducts/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseAddCardImage = {
	isLoadingAddCardImage: ComputedRef<boolean>;
	isErrorAddCardImage: ComputedRef<boolean>;
	addCardImage: Function;
};

export function useAddCardImage(): UseAddCardImage {
	let payloadCache = null;
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => addCardImageService(params),
		onSuccess: () => {
			if (callFunc) callFunc(false, payloadCache);
		},
		onError: error => {
			if (isHttpRequestError(error, 400)) {
				if (callFunc) callFunc(true, { duplicated: true });
				return;
			}
			if (callFunc) callFunc(true, null);
		},
	});

	const isLoadingAddCardImage = computed(() => {
		return isLoading.value;
	});

	const isErrorAddCardImage = computed(() => {
		return isError.value;
	});

	function addCardImage(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
		payloadCache = payload;
	}

	return {
		isLoadingAddCardImage,
		isErrorAddCardImage,
		addCardImage,
	};
}
