import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { builderFinancialVehicleTypes } from '@/features/commercialProducts/helpers/builderFinancialVehicleTypes';
import { getFilterFinancialVehicle as getFilterFinancialVehicleService } from '@/features/commercialProducts/services';

type UseFinancialVehicleTypes = {
	financialVehicleTypes : Ref<Array<any>>;
	isLoadingFinancialVehicleTypes : ComputedRef<boolean>;
	isErrorFinancialVehicleTypes : ComputedRef<boolean>;
	getFinancialVehicleTypes : Function;
};

export function useFinancialVehicleTypes(): UseFinancialVehicleTypes {
	const financialVehicleTypes  = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getFilterFinancialVehicleService(),
		onSuccess: (response) => {
			const data = builderFinancialVehicleTypes(response);
			financialVehicleTypes.value = data;
		},
		onError: () => {
			financialVehicleTypes.value = [];
		},
	});

	const isLoadingFinancialVehicleTypes = computed(() => {
		return isLoading.value;
	});

	const isErrorFinancialVehicleTypes = computed(() => {
		return isError.value;
	});

	function getFinancialVehicleTypes() {
		mutate();
	}

	return {
		financialVehicleTypes,
		isLoadingFinancialVehicleTypes,
		isErrorFinancialVehicleTypes,
		getFinancialVehicleTypes,
	};
}
