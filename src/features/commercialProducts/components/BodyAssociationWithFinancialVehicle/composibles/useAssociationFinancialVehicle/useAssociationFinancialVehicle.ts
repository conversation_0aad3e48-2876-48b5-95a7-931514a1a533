import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import {
	getCommercialProductFinancialVehicleAvailable
} from '@/features/commercialProducts/services';
import {
	builderCommercialProductAssociationFinancialVehicle
} from '@/features/commercialProducts/helpers/builderCommercialProductAssociationFinancialVehicle';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseAssociationFinancialVehicle = {
	associationFinancialVehicle: Ref<Array<any>>;
	isLoadingAssociationFinancialVehicle : ComputedRef<boolean>;
	isErrorAssociationFinancialVehicle : ComputedRef<boolean>;
	getAssociationFinancialVehicle : Function;
};

export function useAssociationFinancialVehicle(): UseAssociationFinancialVehicle {

	const associationFinancialVehicle = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getCommercialProductFinancialVehicleAvailable(params),
		onSuccess: (response) => {
			const data = builderCommercialProductAssociationFinancialVehicle(response);
			associationFinancialVehicle.value = data;
		},
		onError: (error) => {
			if (isHttpRequestError(error, 404)) {
				associationFinancialVehicle.value = [];
				return;
			}
			associationFinancialVehicle.value = [];
		},
	});

	const isLoadingAssociationFinancialVehicle = computed(() => {
		return isLoading.value;
	});

	const isErrorAssociationFinancialVehicle = computed(() => {
		return isError.value;
	});

	function getAssociationFinancialVehicle(payload) {
		mutate(payload);
	}

	return {
		associationFinancialVehicle,
		isLoadingAssociationFinancialVehicle,
		isErrorAssociationFinancialVehicle,
		getAssociationFinancialVehicle,
	};
}
