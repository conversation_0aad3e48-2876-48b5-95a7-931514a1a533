import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import {
	createAssociationCommercialProductFinancialVehicle
} from '@/features/commercialProducts/services';

type UseAddAssociationFinancialVehicle = {
	isLoadingAddAssociationFinancialVehicle : ComputedRef<boolean>;
	isErrorAddAssociationFinancialVehicle : ComputedRef<boolean>;
	createAssociationFinancialVehicle : Function;
};

export function useAddAssociationFinancialVehicle(): UseAddAssociationFinancialVehicle {

	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => createAssociationCommercialProductFinancialVehicle(params),
		onSuccess: () => {
			createNotification();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 1500);
			}
		},
		onError: () => {
			createNotificationError();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 1500);
			}
		},
	});

	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, 'Veículo Financeiro associado com sucesso!');
	}

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			'Não foi possivel associar o Veículo Financeiro. Por favor tente mais tarde.'
		);
	}

	const isLoadingAddAssociationFinancialVehicle = computed(() => {
		return isLoading.value;
	});

	const isErrorAddAssociationFinancialVehicle = computed(() => {
		return isError.value;
	});

	function createAssociationFinancialVehicle(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
	}

	return {
		isLoadingAddAssociationFinancialVehicle,
		isErrorAddAssociationFinancialVehicle,
		createAssociationFinancialVehicle,
	};
}
