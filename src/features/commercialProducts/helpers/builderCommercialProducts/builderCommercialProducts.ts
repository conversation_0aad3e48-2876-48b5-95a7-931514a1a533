import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { COMMERCIAL_PRODUCT_TYPE, LIMITS, STATUS } from '../../constants';
import { fixCommercialProductType } from '../fixCommercialProductType';
import { getFilterCommercialProductTypes } from '../../services';
import { AxiosResponse } from 'axios';

export type CommercialProductsContent = {
	accountProductId: null | number;
	enabled: number;
	endAt: string;
	id: number;
	name: string;
	startAt: string;
	typeId: number;
};

export type CommercialProductsData = {
	content: CommercialProductsContent[];
	page: number;
	size: number;
	totalItems: number;
	totalPages: number;
};

function parseNameStatus(value: number): string {
	return STATUS[value];
}

function formatDate(value: string): string {
	return defaultDateFormat(value) || '-';
}

export async function builderCommercialProducts(response: AxiosResponse<CommercialProductsData>) {
	const { data: productTypesData } = await getFilterCommercialProductTypes();

	const productTypesObj = productTypesData.reduce(
		(acc: Record<number, string>, item: { id: number; type: string }) => {
			acc[item.id] = item.type;
			return acc;
		},
		{}
	);

	const { content, page, size, totalItems, totalPages } = response.data;
	const newData = content.map(item => ({
		id: item.id,
		name: item.name,
		dateStart: formatDate(item.startAt),
		dateEnd: formatDate(item.endAt),
		status: parseNameStatus(item.enabled),
		type: fixCommercialProductType(productTypesObj[item.typeId]),
	}));

	return {
		content: newData,
		pagination: {
			pageNumber: page,
			pageSize: size,
			sort: null,
			totalElements: totalItems,
			totalPages: totalPages,
		},
	};
}
