const operationName = {
	1:'<PERSON><PERSON><PERSON>',
	2:'Origin<PERSON><PERSON>',
	3:'Crédit<PERSON>'
};

const modalityType = {
	1:'<PERSON><PERSON>',
	2:'<PERSON><PERSON><PERSON> em Conta',
	3:'<PERSON><PERSON><PERSON>'
};

function formatEdit(data){
	const newData = data.map((item)=>{
		return {
			operationModality: item.operationModality,
			settlementTypeStatus: item.settlementTypeStatus
		};
	});
	return newData;
}

function formatPageList(data){
	const newData = data.map((item, index)=>{
		return {
			...item,
			operationModalityName: operationName[item.operationModality],
			key: `${item.operationModalityName}-${index}`,
			settlementTypeStatus:item.settlementTypeStatus.map((t, i)=>{
				return{
					...t,
					key:`${t.settlementTypeName}-${index}-${i}`,
					settlementTypeName: modalityType[t.settlementType],
					selected: t.status === 1
				};
			})
		};
	});
	return newData;
}

export function builderCommercialProductMobility(response) {
	const {content} = response.data;
	const edit = formatEdit(content);
	const list = formatPageList(content);
	return {
		edit,
		list
	};
}
