import {
	RequestStatusEnum,
	errorBuilder,
	queryString,
} from '@farm-investimentos/front-mfe-libs-ts';

import { responseEmptyBuilder } from '@/helpers/responseEmptyBuilder';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

import { 
	getAvailableCash as getAvailableCashService,
	saveAvailableCash as saveAvailableCashService
} from '../services';

export default {
	async getAvailableCash({ commit }, { filters }) {
		commit('setAvailableCashRequestStatus', RequestStatusEnum.START);
		try {
			const params = queryString(filters, {});
			const response = await getAvailableCashService(params);
			commit('setAvailableCashData', response.data);
			commit('setAvailableCashRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			if (isHttpRequestError(error, 404)) {
				responseEmptyBuilder(commit, 'AvailableCashData', 'AvailableCash');
				return;
			}
			commit('setAvailableCashRequestStatus', errorBuilder(error));
		}
	},

	async saveAvailableCash({ commit }, { payload }) {
		commit('setSaveAvailableCashRequestStatus', RequestStatusEnum.START);
		try {
			await saveAvailableCashService(payload);
			commit('setSaveAvailableCashRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setSaveAvailableCashRequestStatus', errorBuilder(error));
		}
	},
};
