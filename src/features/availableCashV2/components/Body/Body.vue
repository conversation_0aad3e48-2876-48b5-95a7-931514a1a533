<template>
	<farm-container>
		<farm-form ref="form" v-model="disabledButtonFooter" v-if="!isError">
			<farm-row>
				<farm-col cols="12">
					<farm-alertbox color="warning" icon="alert-outline" dismissable>
						{{ $tc('availableCash.alert') }}
					</farm-alertbox>
				</farm-col>
			</farm-row>
			<AvailableCashList
				:data="dataList"
				@updatedAvailableCashValues="updatedAvailableCashValues"
				@reValidate="reValidate"
			/>
			<farm-row extra-decrease v-if="isPagination">
				<farm-box>
					<farm-datatable-paginator
						:perPageOptions="perPageOptions"
						:initialLimitPerPage="initialLimitPerPage"
						:page="currentPage"
						:totalPages="totalPages"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangeLimitPerPage"
					/>
				</farm-box>
			</farm-row>
		</farm-form>
		<FooterForm
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="true"
			:hiddenButtonCancel="true"
			:data="dataFooterForm"
			@onSave="onSave"
		/>
		<farm-loader v-if="isLoading" mode="overlay" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { createObject } from '@/helpers/createObject';

import AvailableCashList from '../AvailableCashList';

export default defineComponent({
	mixins: [pageable],
	components: {
		FooterForm,
		AvailableCashList,
	},
	data() {
		return {
			disabledButtonFooter: true,
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			lastSearchFilters: {},
			filters: {
				page: 0,
				limit: 12,
			},
			perPageOptions: [6, 12, 24, 60, 120],
			initialLimitPerPage: 12,
			dataList: [],
			totalPages: 0,
			itemsUpdated: [],
		};
	},
	computed: {
		...mapGetters('availableCash', {
			availableCashData: 'availableCashData',
			availableCashRequestStatus: 'availableCashRequestStatus',
			saveAvailableCashRequestStatus: 'saveAvailableCashRequestStatus',
		}),
		isLoading(): boolean {
			return (
				this.availableCashRequestStatus === RequestStatusEnum.START ||
				this.saveAvailableCashRequestStatus === RequestStatusEnum.START
			);
		},
		isError(): boolean {
			return (
				this.availableCashRequestStatus.type === RequestStatusEnum.START ||
				this.saveAvailableCashRequestStatus.type === RequestStatusEnum.START
			);
		},
		isPagination(): boolean {
			return !this.isError && this.dataList.length > 0;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('availableCash', {
			getAvailableCash: 'getAvailableCash',
			saveAvailableCash: 'saveAvailableCash',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getAvailableCash({ filters: this.filters });
		},
		onSave(): void {
			this.saveAvailableCash({
				payload: this.itemsUpdated,
			});
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.dataFooterForm = data;
		},
		updatedAvailableCashValues(value): void {
			if (this.itemsUpdated.length === 0) {
				this.itemsUpdated = [value];
				return;
			}

			const hasItemData = this.itemsUpdated.filter(item => {
				return item.id === value.id;
			});

			if (hasItemData.length > 0) {
				const newData = this.itemsUpdated.map(item => {
					if (item.id === value.id) {
						return {
							...item,
							availableCash: value.availableCash,
						};
					}
					return item;
				});
				this.itemsUpdated = [...newData];
			} else {
				this.itemsUpdated = [...this.itemsUpdated, value];
			}
		},
		reValidate(): void {
			setTimeout(() => {
				this.$refs.form.restart();
			}, 100);
		}
	},
	watch: {
		availableCashRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataList = this.availableCashData.content;
				this.totalPages = this.availableCashData.totalPages;
			}
		},
		saveAvailableCashRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(
					RequestStatusEnum.SUCCESS,
					this.$tc('availableCash.notification.success')
				);
				setTimeout(() => {
					this.doSearch();
				}, 1500);
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					this.$tc('availableCash.notification.error') + newValue.message
				);
			}
		},
	},
});
</script>
