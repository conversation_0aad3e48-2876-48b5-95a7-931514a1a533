import client from '@/configurations/services/registerV3';

export const getAvailableCash = (filters) => {
	const url = `api/v1/financial-vehicle/avaliable-cash`;
	let query = '';
	if (filters) {
		query = `?${filters}`;
		if (query.indexOf('limit') === -1) {
			query = `${query}&limit=10`;
		}
	}
	return client.get(`${url}${query}`);
};

export const saveAvailableCash = payload => {
	return client.patch(`/api/v1/financial-vehicle/available-cash`, payload);
};