import { CampaignDTO } from '../../../helpers/dtos/campaignToAPI';

import type { RequestQueryString } from '@/types';
import type { Campaign } from '../types';

export type GetCampaignListRequest = {
	query: RequestQueryString;
};

export type GetCampaignDetailRequest = {
	params: {
		id: Campaign['id'];
	};
};

export type GetCampaignHistoryRequest = {
	query: RequestQueryString;
};

export type CreateOrUpdateCampaignRequest = {
	payload: Campaign | CampaignDTO;
	params?: {
		campaignId: number;
	};
};

export type UpdateCampaignStatusRequest = {
	payload: {
		status: Campaign['status'];
	};
	params: {
		campaignId: number;
	};
};

export type GetFinancialVehiclesRequest = {
	query: RequestQueryString;
};

export type GetCommercialProductsRequest = {
	query: RequestQueryString;
};

export type GetCampaignTimelineRequest = {
	query: {
		commercial_prd_id: number;
		operation_type?: number;
		campaign_status?: number | number[];
	};
};
