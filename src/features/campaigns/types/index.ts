export type CampaignOperation = {
	baseId?: number; // ID used to identify resource in database, used in edit action
	id?: number;
	interval: [string, string] | null;
	disbursement: [string, string] | null;
};

export type CampaignCommercialProduct = {
	baseId?: number;
	id?: number;
	name?: string;
	baseTax: string | null;
	commercialProductTax: string | null;
	validity: [string, string] | null;
	status: boolean;
	operations: CampaignOperation[];
};
export type Campaign = {
	id?: number;
	name: string | null;
	validity: [string, string] | null;
	status: boolean;
	updatedByName: string;
	updatedBy: string;
	updatedAt: string;
	commercialProducts: CampaignCommercialProduct[];
};

export type CampaignTimelineCampaign = {
	name: string;
	id: number;
	status: boolean;
	startDate: string;
	endDate: string;
	tax: number;
};

export type CampaignTimelineCommercialProduct = {
	name: string;
	id: number;
	status: boolean;
	startDate: string;
	endDate: string;
	operation_type: number;
	startDueDate: string;
	endDueDate: string;
	startDisbursementDate: string;
	endDisbursementDate: string;
};

export type CampaignTimelineItem = {
	campaign: CampaignTimelineCampaign;
	commercialProduct: CampaignTimelineCommercialProduct;
};

export type CampaignTimelineResponse = {
	data: {
		content: CampaignTimelineItem[];
	};
};
