<template>
	<farm-container>
		<CampaignForm v-if="form" :form="form" />
		<farm-loader v-else-if="isLoading" mode="overlay" />
		<farm-emptywrapper
			v-else
			title="Não há campanha com esse ID"
			subtitle="Volte e tente novamente"
			:bordered="false"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, ref, watch } from 'vue';
import { useRouter, useIsLoading } from '@/composibles';

import CampaignForm from '../components/CampaignForm';
import { useCampaign } from '../composables';

export default defineComponent({
	components: {
		CampaignForm,
	},
	setup() {
		const router = useRouter();
		const { campaignDetail, campaignDetailRequestStatus, fetchCommercialProducts, fetchCampaignDetail } =
			useCampaign();

		const form = ref();

		const isLoading = useIsLoading([campaignDetailRequestStatus]);

		watch(campaignDetail, newValue => {
			form.value = newValue;
		});

		onBeforeMount(() => {
			form.value = null;
			fetchCommercialProducts();
			fetchCampaignDetail(Number(router.currentRoute.params.id));
		});

		return {
			form,
			isLoading,
		};
	},
});
</script>
