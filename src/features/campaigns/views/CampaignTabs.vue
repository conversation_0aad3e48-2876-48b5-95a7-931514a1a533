<template>
	<farm-container>
		<farm-row extra-decrease>
			<farm-tabs
				ref="tabEl"
				:allowUserChange="true"
				:showCounter="false"
				:tabs="tabs"
				class="mt-n6 mb-6"
				@update="updateTab"
			/>
		</farm-row>

		<TabList v-if="currentTab === CAMPAIGN_LIST" />
		<TabCustomFilter v-if="currentTab === CAMPAIGN_CUSTOM_FILTER" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { TabTypes } from '@/types';
import { CAMPAIGN_LIST, CAMPAIGN_CUSTOM_FILTER } from '../constants';

import TabList from '../components/TabList';
import TabCustomFilter from '../components/TabCustomFilter';
import { tabDefault } from '../configurations/tabs';

export default defineComponent({
	components: {
		TabList,
		Tab<PERSON>ustomFilter,
	},
	data() {
		return {
			tabs: tabDefault,
			currentTab: CAMPAIGN_LIST,
			CAMPAIGN_LIST,
			CAMPAIGN_CUSTOM_FILTER,
		};
	},
	mounted(): void {
		this.initializeTab();
	},
	methods: {
		updateTab(item: TabTypes): void {
			if (!item) {
				return;
			}
			if (item.path === this.$route.query.path) {
				return;
			}
			this.$router.replace({
				query: {
					path: item.path,
				},
			});
			this.currentTab = item.path;
		},
		initializeTab(): void {
			let path = this.$route.query.path;
			if (!path) {
				path = CAMPAIGN_LIST;
				this.$router.replace({
					path: this.$route.path,
					query: { path },
				});
			}
			this.currentTab = path;
			const index = this.tabs.findIndex(tab => path === tab.path);
			if (this.$refs.tabEl && this.$refs.tabEl.toIndex) {
				this.$refs.tabEl.toIndex(index);
			}
		},
	},
	watch: {
		'$route.query.path'(newPath: string): void {
			if (newPath && this.currentTab !== newPath) {
				this.currentTab = newPath;
				
		
				const tabIndex = this.tabs.findIndex(tab => tab.path === newPath);
				if (this.$refs.tabEl && this.$refs.tabEl.toIndex && tabIndex >= 0) {
					this.$refs.tabEl.toIndex(tabIndex);
				}
			}
		},
	},
});
</script>