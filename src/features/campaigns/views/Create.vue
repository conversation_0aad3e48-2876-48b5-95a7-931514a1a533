<template>
	<farm-container>
		<CampaignForm />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount } from 'vue';

import CampaignForm from '../components/CampaignForm';
import { useCampaign } from '../composables';

export default defineComponent({
	components: {
		CampaignForm,
	},
	setup() {
		const { fetchCommercialProducts } = useCampaign();

		onBeforeMount(() => {
			fetchCommercialProducts();
		});

		return {};
	},
});
</script>
