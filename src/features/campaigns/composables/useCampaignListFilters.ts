import { computed, ref } from 'vue';

export default function useCampaignListFilter() {
	const filterFormComponent = ref();
	const formFilters = ref({
		busca: '',
		campaignStatus: null,
		campaignCommercialProductStatus: null,
	});
	const isShowingFilters = ref(false);

	const campaignFilters = computed(() => {
		return Object.entries(formFilters.value).reduce((accumulator, [key, value]) => {
			const isInvalidValue = value === undefined || value === null || value === '';
			if (isInvalidValue) {
				return accumulator;
			}

			return {
				...accumulator,
				[key]: String(value),
			};
		}, {});
	});

	const toggleFilters = () => (isShowingFilters.value = !isShowingFilters.value);

	return {
		filterFormComponent,
		formFilters,
		campaignFilters,
		isShowingFilters,
		toggleFilters,
	};
}
