import { computed, ref } from 'vue';

import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { useGetter, useStore } from '@/composibles';

import type {
	GetCommercialProductsRequest,
	GetCampaignHistoryRequest,
	GetCampaignDetailRequest,
	GetCampaignListRequest,
	GetCampaignTimelineRequest,
} from '../services/types';
import type { Campaign } from '../types';

const campaignModals = ref({
	history: {
		status: false,
		id: null,
	},
});

export default function useCampaign() {
	const store = useStore();

	const commercialProductsList = computed(useGetter('campaigns', 'commercialProductsList'));

	const campaignList = computed(useGetter('campaigns', 'campaignList'));
	const campaignListPageable = computed(useGetter('campaigns', 'campaignListPageable'));

	const campaignDetail = computed(useGetter('campaigns', 'campaignDetail'));

	const campaignHistoryList = computed(useGetter('campaigns', 'campaignHistoryList'));
	const campaignHistoryListPageable = computed(
		useGetter('campaigns', 'campaignHistoryListPageable')
	);

	const campaignTimeline = computed(useGetter('campaigns', 'campaignTimeline'));

	const commercialProductsListRequestStatus = computed(
		useGetter('campaigns', 'commercialProductsListRequestStatus')
	);
	const campaignListRequestStatus = computed(useGetter('campaigns', 'campaignListRequestStatus'));
	const campaignHistoryListRequestStatus = computed(
		useGetter('campaigns', 'campaignHistoryListRequestStatus')
	);
	const campaignDetailRequestStatus = computed(
		useGetter('campaigns', 'campaignDetailRequestStatus')
	);
	const createOrUpdateCampaignRequestStatus = computed(
		useGetter('campaigns', 'createOrUpdateCampaignRequestStatus')
	);
	const campaignTimelineRequestStatus = computed(
		useGetter('campaigns', 'campaignTimelineRequestStatus')
	);

	const hasSuccessCreatingCampaign = computed(
		() => createOrUpdateCampaignRequestStatus.value === RequestStatusEnum.SUCCESS
	);

	const fetchCampaignList = (newRequest?: GetCampaignListRequest['query']) => {
		const defaultFilter = {
			query: {
				page: '0',
				limit: '5',
			},
		};

		const request: GetCampaignListRequest = newRequest ? { query: newRequest } : defaultFilter;

		store.dispatch('campaigns/getCampaignList', request);
	};
	const fetchCampaignDetail = (id: Campaign['id']) => {
		const request: GetCampaignDetailRequest = {
			params: {
				id,
			},
		};

		store.dispatch('campaigns/getCampaignDetail', request);
	};
	const fetchCampaignHistoryList = (
		id: Campaign['id'],
		newRequest?: GetCampaignListRequest['query']
	) => {
		const params = new URLSearchParams(newRequest);
		const defaultFilter = {
			query: {
				idCampaign: id.toString(),
				page: '0',
				limit: '5',
			},
		};

		const request: GetCampaignHistoryRequest = {
			query: {
				...defaultFilter.query,
				...Object.fromEntries(params),
			},
		};

		store.dispatch('campaigns/getCampaignHistory', request);
	};

	const fetchCommercialProducts = () => {
		const request: GetCommercialProductsRequest = {
			query: 'page=0&limit=50&order=ASC&orderby=name',
		};

		store.dispatch('campaigns/getCommercialProductsList', request);
	};

	const fetchCampaignTimeline = (request: GetCampaignTimelineRequest) => {
		store.dispatch('campaigns/getCampaignTimeline', request);
	};

	const toggleCampaignModal = (type: keyof typeof campaignModals.value, id?: number) => {
		const modal = campaignModals.value[type];

		modal.status = !modal.status;
		modal.id = modal.status ? id : null;

		if (!modal.id) {
			store.commit('campaigns/setCampaignHistoryList', []);
			store.commit('campaigns/setCampaignHistoryListPageable', null);
		}
	};

	return {
		campaignModals,
		campaignList,
		campaignListPageable,
		campaignListRequestStatus,
		campaignDetail,
		campaignDetailRequestStatus,
		campaignHistoryList,
		campaignHistoryListRequestStatus,
		campaignHistoryListPageable,
		campaignTimeline,
		campaignTimelineRequestStatus,
		createOrUpdateCampaignRequestStatus,
		hasSuccessCreatingCampaign,
		commercialProductsList,
		commercialProductsListRequestStatus,
		fetchCampaignList,
		fetchCampaignDetail,
		fetchCampaignHistoryList,
		fetchCommercialProducts,
		fetchCampaignTimeline,
		toggleCampaignModal,
	};
}
