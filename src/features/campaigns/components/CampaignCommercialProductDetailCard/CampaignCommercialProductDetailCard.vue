<template>
	<article>
		<div class="mb-6">
			<div class="d-flex flex-column justify-space-between flex-sm-row mb-2">
				<div class="mb-2 mb-sm-0">
					<farm-caption variation="semiBold" tag="span">Produto Comercial</farm-caption>:
					{{ commercialProduct.name }}
					<farm-chip
						:color="commercialProduct.status ? 'primary' : 'neutral'"
						dense
						class="ml-2"
					>
						{{ commercialProduct.status ? 'Ativa' : 'Inativa' }}
					</farm-chip>
				</div>
				<div>
					<farm-caption variation="semiBold" tag="span"> Taxa Cliente (a.m.)</farm-caption
					>: {{ commercialProduct.baseTax }}%
				</div>
			</div>
			<div class="d-flex flex-column justify-space-between flex-sm-row">
				<div>
					<farm-caption variation="semiBold" tag="span">
						Vigência disponível para o usuário </farm-caption
					>: {{ defaultDateFormat(commercialProduct.validity[0]) || 'Não aferido' }} até
					{{ defaultDateFormat(commercialProduct.validity[1]) || 'Não aferido' }}
				</div>
				<div>
					<farm-caption variation="semiBold" tag="span"> Taxa Fundo (a.m.)</farm-caption>:
					{{ commercialProduct.commercialProductTax }}%
				</div>
			</div>
		</div>

		<DashedCard
			v-for="(operation, index) in commercialProduct.operations"
			:key="operation.id"
			class="pa-4"
			:class="{
				'mb-6': lastOperationHasMargin || index !== commercialProduct.operations.length - 1,
			}"
		>
			<div class="dashed-card__item">
				<farm-idcaption copy-text="">
					<template v-slot:title> Operação </template>
					<template v-slot:subtitle>
						{{
							OPERATION_TYPES_ENUM[operation.id]
								? OPERATION_TYPES_ENUM[operation.id].name
								: 'Não encontrado'
						}}
					</template>
				</farm-idcaption>
			</div>
			<div class="dashed-card__item">
				<farm-idcaption copy-text="">
					<template v-slot:title> Intervalo Vencimento </template>
					<template v-slot:subtitle>
						{{ defaultDateFormat(operation.interval[0]) || 'Não aferido' }} até
						{{ defaultDateFormat(operation.interval[1]) || 'Não aferido' }}
					</template>
				</farm-idcaption>
			</div>
			<div class="dashed-card__item">
				<farm-idcaption copy-text="">
					<template v-slot:title> Período de Desembolso Início/Fim </template>
					<template v-slot:subtitle>
						{{ defaultDateFormat(operation.disbursement[0]) || 'Não aferido' }}
						até
						{{ defaultDateFormat(operation.disbursement[1]) || 'Não aferido' }}
					</template>
				</farm-idcaption>
			</div>
		</DashedCard>

		<slot name="line" />
	</article>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { OPERATION_TYPES_ENUM } from '@/constants';

import DashedCard from '@/components/DashedCard';

import type { PropType } from 'vue';
import type { CampaignCommercialProduct } from '../../types';

export default defineComponent({
	components: {
		DashedCard,
	},
	props: {
		commercialProduct: {
			type: Object as PropType<CampaignCommercialProduct>,
			required: true,
		},
		lastOperationHasMargin: {
			type: Boolean,
			default: true,
		},
	},
	setup() {
		return {
			OPERATION_TYPES_ENUM,
			defaultDateFormat,
		};
	},
});
</script>

<style lang="scss" scoped>
@import 'CampaignCommercialProductDetailCard';
</style>
