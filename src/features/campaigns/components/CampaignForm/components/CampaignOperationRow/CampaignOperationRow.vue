<template>
	<farm-row>
		<farm-col cols="12" md="4" lg="3">
			<farm-label :for="`operation-id-${commercialProductId}-${index}`" required> Operação </farm-label>
			<farm-select
				v-model="operation.id"
				:id="`operation-id-${commercialProductId}-${index}`"
				:items="operations"
				:rules="[rules.required]"
			/>
		</farm-col>
		<farm-col cols="12" md="4" lg="3">
			<farm-label :for="`operation-interval-${commercialProductId}-${index}`" required>
				Intervalo Vencimento
			</farm-label>
			<farm-input-rangedatepicker
				v-model="operation.interval"
				required
				:input-id="`operation-interval-${commercialProductId}-${index}`"
			/>
		</farm-col>
		<farm-col cols="12" md="4" lg="3">
			<farm-label :for="`operation-disbursement-${commercialProductId}-${index}`" required>
				Período de Desembolso Início/Fim
			</farm-label>
			<farm-input-rangedatepicker
				:input-id="`operation-disbursement-${commercialProductId}-${index}`"
				v-model="operation.disbursement"
				required
			/>
		</farm-col>
		<slot />
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { PropType } from 'vue/types/v3-component-props';

import { useCampaignForm } from '../../composables';
import type { CampaignOperation } from '../../../../types';

export default defineComponent({
	props: {
		commercialProductId: {
			type: Number,
			required: true,
		},
		operations: {
			type: Array,
			required: true,
		},
		operation: {
			type: Object as PropType<CampaignOperation>,
			required: true,
		},
		index: {
			type: Number,
			required: true,
		},
	},
	setup() {
		const { rules } = useCampaignForm();

		return {
			rules,
		};
	},
});
</script>
