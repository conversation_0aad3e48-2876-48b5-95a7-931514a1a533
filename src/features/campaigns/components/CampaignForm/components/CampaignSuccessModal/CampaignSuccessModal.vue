<template>
	<farm-modal size="sm" :value="value" :offset-top="48" :offset-bottom="80" persistent>
		<template #header>
			<farm-dialog-header title="Sucesso" :has-close-icon="false" />
		</template>
		<template #content>
			<farm-bodytext type="2">
				Seus dados foram salvos e a criação foi efetuada com sucesso
			</farm-bodytext>
		</template>
		<template #footer>
			<farm-line no-spacing />
			<div class="pa-8" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup() {
		return {};
	},
});
</script>
