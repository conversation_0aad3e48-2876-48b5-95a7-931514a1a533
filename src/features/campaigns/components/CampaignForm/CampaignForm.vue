<template>
	<farm-form v-model="isValidForm" ref="formComponent">
		<farm-row class="mb-6">
			<farm-col cols="12" md="4" lg="3">
				<farm-label for="campaign-name" required>Nome da Campanha</farm-label>
				<farm-textfield-v2
					id="campaign-name"
					v-model="form.name"
					:rules="[rules.required]"
				/>
			</farm-col>

			<farm-col cols="12" md="4" lg="3">
				<farm-label for="campaign-validity" required>
					Vigência disponível para o usuário
				</farm-label>
				<farm-input-rangedatepicker
					v-model="form.validity"
					input-id="campaign-validity"
					required
				/>
			</farm-col>

			<farm-col cols="12" md="4" lg="3">
				<farm-label required>Status da campanha</farm-label>
				<farm-switcher id="campaign-status" v-model="form.status" />
			</farm-col>
		</farm-row>

		<farm-line no-spacing class="mb-6" />

		<farm-subtitle tag="h3" class="mb-6">
			Produtos comerciais atrelados à campanha:
		</farm-subtitle>
		<farm-subtitle tag="h4" type="2" variation="regular" class="mb-6">
			O agrupamento proporciona a configuração de 1 ou mais produtos comerciais de maneira
			ágil e otimizada.
		</farm-subtitle>

		<CampaignCommercialProductCard
			v-for="(commercialProduct, index) in form.commercialProducts"
			:key="index"
			:id="index"
			:commercialProduct="commercialProduct"
			:title="`Produto Comercial ${index + 1}`"
			:has-delete-button="form.commercialProducts.length > 1"
			:campaign-validity="form.validity"
			@remove-commercial-product-card="removeCommercialProductCard(index)"
		/>

		<farm-btn :disabled="!hasMoreCommercialProductsToAdd" outlined class="mt-6" @click="addCommercialProductCard">
			<farm-icon>plus</farm-icon> Adicionar Produto Comercial
		</farm-btn>

		<div class="mx-n6 mt-6">
			<farm-line />
		</div>

		<footer class="d-flex align-end justify-end pt-6">
			<farm-btn outlined class="mr-2" @click="toggleCancelModal">Cancelar</farm-btn>
			<farm-btn @click="createOrUpdateCampaign" :disabled="!isValidForm">
				{{ mainActionText }}
			</farm-btn>
		</footer>

		<CampaignSuccessModal
			v-if="hasSuccessCreatingCampaign"
			:value="hasSuccessCreatingCampaign"
		/>
		<CampaignCancelModal
			v-if="isCancellingOperation"
			:value="isCancellingOperation"
			@on-close="toggleCancelModal"
		/>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-form>
</template>

<script lang="ts">
import {
	getCurrentInstance,
	defineComponent,
	defineAsyncComponent,
	computed,
	watch,
} from 'vue';

import { useIsLoading } from '@/composibles';

import { DEFAULT_FORM_STRUCTURE, DEFAULT_COMMERCIAL_PRODUCT_CARD_STRUCTURE } from './constants';
const CampaignSuccessModal = defineAsyncComponent(
	() => import('./components/CampaignSuccessModal')
);
const CampaignCancelModal = defineAsyncComponent(() => import('./components/CampaignCancelModal'));

import { useCampaignForm } from './composables';

import { useCampaign } from '../../composables';

import type { PropType } from 'vue';
import type { Campaign } from '../../types';
import CampaignCommercialProductCard
	from '@/features/campaigns/components/CampaignForm/components/CampaignCommercialProductCard';

export default defineComponent({
	components: {
		CampaignCommercialProductCard,
		CampaignSuccessModal,
		CampaignCancelModal,
	},
	props: {
		form: {
			type: Object as PropType<Campaign>,
			required: false,
			default: () => JSON.parse(JSON.stringify(DEFAULT_FORM_STRUCTURE)),
		},
	},
	setup(props) {
		const internalInstance = getCurrentInstance().proxy;
		const {
			commercialProductsListRequestStatus,
			createOrUpdateCampaignRequestStatus,
			hasSuccessCreatingCampaign,
		} = useCampaign();
		const {
			form,
			formComponent,
			isValidForm,
			rules,
			selectedCommercialProducts,
			hasMoreCommercialProductsToAdd,
			createOrUpdateCampaign,
			isEditPage,
			isCancellingOperation,
			toggleCancelModal,
		} = useCampaignForm(props.form as Campaign);

		const mainActionText = computed(() => (isEditPage.value ? 'Editar' : 'Criar'));

		const isLoading = useIsLoading([
			commercialProductsListRequestStatus	,
			createOrUpdateCampaignRequestStatus,
		]);

		const addCommercialProductCard = () => {
			const structure = JSON.parse(JSON.stringify(DEFAULT_COMMERCIAL_PRODUCT_CARD_STRUCTURE));

			form.value.commercialProducts.push(structure);
			formComponent.value.restart();
		};
		const removeCommercialProductCard = (index: number) => {
			form.value.commercialProducts.splice(index, 1);
			formComponent.value.restart();
		};

		watch(() => form.value.validity, (newValidity) => {
			if (!newValidity || newValidity.length !== 2) return;

			form.value.commercialProducts.forEach(product => {
				product.validity = [];
			});
			
			formComponent.value.restart();
		});

	

		return {
			formComponent,
			isLoading,
			isValidForm,
			isCancellingOperation,
			form,
			rules,
			selectedCommercialProducts,
			hasMoreCommercialProductsToAdd,
			hasSuccessCreatingCampaign,
			mainActionText,
			createOrUpdateCampaign: () => createOrUpdateCampaign(internalInstance.$dialog),
			addCommercialProductCard,
			removeCommercialProductCard,
			toggleCancelModal,
		};
	},
});
</script>
