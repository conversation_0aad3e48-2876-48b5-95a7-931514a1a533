import { ref } from 'vue';
import { PatchCfopItem, patchFinancialVehicleCfops } from '@/services/cfop';

export function usePatchFinancialVehicleCfops() {
	const state = ref<'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR'>('IDLE');
	const successMessage = ref<string | null>(null);
	const error = ref<string | null>(null);

	const patchCfops = async (id: number, cfops: PatchCfopItem[]) => {
		state.value = 'LOADING';
		error.value = null;
		successMessage.value = null;

		try {
			const response = await patchFinancialVehicleCfops(id, { cfops });
			successMessage.value = response.data.message || 'CFOP updated successfully!';
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		successMessage,
		error,
		patchCfops,
	};
}
