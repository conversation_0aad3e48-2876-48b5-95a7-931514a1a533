import { ref } from 'vue';
import {
	fetchFinancialVehicleCfops,
	FetchFinancialVehicleCFOPSQuery,
	FinancialVehicleCfopsResponse,
} from '@/services/cfop';

export function useFetchFinancialVehicleCfops() {
	const state = ref<'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR'>('IDLE');
	const data = ref<FinancialVehicleCfopsResponse>(null);
	const error = ref<string | null>(null);

	const fethCfops = async (id: number, query?: FetchFinancialVehicleCFOPSQuery) => {
		state.value = 'LOADING';
		error.value = null;

		try {
			const response = await fetchFinancialVehicleCfops(id, query);
			data.value = response.data;
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		fethCfops,
	};
}
