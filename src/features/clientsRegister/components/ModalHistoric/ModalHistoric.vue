<template>
	<farm-modal v-model="inputVal" :offsetTop="47" :offsetBottom="68">
		<template v-slot:header>
			<farm-dialog-header title="Histórico" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-row class="mx-n4">
				<farm-box>
					<v-data-table
						class="elevation-0"
						id="table-invites-historic"
						hide-default-footer
						item-key="id"
						:items="itemsInviteHistoric"
						:headers="headers"
						:server-items-length="itemsInviteHistoric.length"
					>
						<template slot="no-data">
							<DataTableEmptyWrapper />
						</template>

						<template v-slot:[`item.status`]="{ item }">
							<div class="d-flex align-center space-between">
								<ChipInviteStatus
									:status="
										item.previousStatusId === null ? 10 : item.previousStatusId
									"
									isfull
								/>
								<span class="icon mdi mdi-arrow-right"></span>

								<ChipInviteStatus :status="item.statusId" isfull />
							</div>
						</template>

						<template v-slot:[`item.createdAt`]="{ item }">
							{{ dateFormat(item.createdAt) }}
						</template>

						<template v-slot:[`item.message`]="{ item }">
							{{ item.message }}
						</template>
					</v-data-table>

					<farm-loader mode="overlay" v-if="isLoading" />
				</farm-box>
			</farm-row>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer @onClose="close" closeLabel="Fechar" :hasConfirm="false" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { mapActions, mapGetters } from 'vuex';
import { defineComponent } from 'vue';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import {
	modalDocumentsHeaders as documentsHeaders,
	tableInviteHistoricHeaders,
} from '../../configurations/headers';

export default defineComponent({
	data() {
		return {
			documentsHeaders,
			headers: [...tableInviteHistoricHeaders],
		};
	},
	components: {},
	mixins: [],
	props: {
		value: {
			required: true,
		},
		itemsInviteHistoric: {
			required: false,
			type: Array,
		},
	},
	computed: {
		...mapGetters('onboarding', {
			historicInviteListRequestStatus: 'historicInviteListRequestStatus',
		}),
		inputVal: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			},
		},
		isLoading() {
			return this.historicInviteListRequestStatus === RequestStatusEnum.START;
		},
	},
	methods: {
		...mapActions('onboarding', {}),
		close() {
			this.inputVal = false;
		},

		dateFormat(data) {
			return new Intl.DateTimeFormat('default', {
				hour: 'numeric',
				minute: 'numeric',
				year: 'numeric',
				month: 'numeric',
				day: 'numeric',
			})
				.format(new Date(data))
				.split(' ')
				.join(' às ');
		},
	},
});
</script>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';

@include stickytable('#table-cadastros-clients-documents-list', 0);
@include stickytable('#table-invites-historic', 0);
</style>
<style lang="scss" scoped>
@import './ModalHistoric';
</style>
