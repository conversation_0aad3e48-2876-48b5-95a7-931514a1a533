<template>
	<farm-modal v-model="inputVal" :offsetTop="48" :offsetBottom="68">
		<template v-slot:header>
			<farm-dialog-header title="Convidar" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-row>
				<farm-box>
					<v-data-table
						v-if="!isError"
						id="table-cadastros-clients-invites-list"
						hide-default-footer
						class="elevation-0"
						v-model="selectedItems"
						:headers="headers"
						:show-select="true"
						:items="signatoriesList"
						:server-items-length="signatoriesList.length"
					>
						<template slot="no-data">
							<DataTableEmptyWrapper />
						</template>
					</v-data-table>
				</farm-box>

				<farm-col v-if="isError">
					<AlertReload label="Ocorreu um erro" @onClick="reload" />
				</farm-col>
				<farm-loader v-if="isLoading" mode="overlay" />
			</farm-row>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmIcon="email"
				:isConfirmDisabled="isConfirmDisabled"
				@onClose="close"
				@onConfirm="confirm"
			/>
		</template>
	</farm-modal>
</template>
<script lang="ts">
import { mapActions, mapGetters } from 'vuex';
import { defineComponent } from 'vue';
import {
	RequestStatusEnum,
	notificationWrapper,
} from '@farm-investimentos/front-mfe-libs-ts';

import { inviteHeaders } from '../../configurations/headers';

export default defineComponent({
	name: 'ModalInvite',
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		item: {
			type: Object,
		},
		idProduct: {
			type: Number,
			required: true,
		},
	},
	computed: {
		...mapGetters('onboarding', {
			signatoriesListRequestStatus: 'signatoriesListRequestStatus',
			signatoriesList: 'signatoriesList',
			sendInviteToSignatoriesRequestStatus: 'sendInviteToSignatoriesRequestStatus',
		}),
		isLoading() {
			return (
				this.signatoriesListRequestStatus === RequestStatusEnum.START ||
				this.sendInviteToSignatoriesRequestStatus === RequestStatusEnum.START
			);
		},
		isError() {
			return this.signatoriesListRequestStatus.type === RequestStatusEnum.ERROR;
		},
		isConfirmDisabled() {
			return this.selectedItems.length === 0;
		},
		inputVal: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			},
		},
	},
	watch: {
		signatoriesList(newValue) {
			this.selectedItems = newValue;
		},
		sendInviteToSignatoriesRequestStatus(newValue) {
			notificationWrapper(newValue, 'Convite(s) enviado(s)', 'ao enviar o(s) convite(s)');
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.$emit('onSendSuccess');
				this.close();
			}
		},
	},
	data() {
		return {
			headers: inviteHeaders,
			selectedItems: [],
		};
	},
	mounted() {
		this.dismissSignatories();
		this.reload();
	},
	methods: {
		...mapActions('onboarding', {
			dismissSignatories: 'dismissSignatories',
			fetchSignatories: 'fetchSignatories',
			sendInviteToSignatories: 'sendInviteToSignatories',
		}),
		close() {
			this.inputVal = false;
		},
		confirm() {
			this.sendInviteToSignatories({
				idProduct: this.idProduct,
				ids: this.selectedItems.map(item => item.id),
				idRegister: this.item.registryId,
			});
		},
		reload() {
			this.fetchSignatories({
				idProduct: this.idProduct,
				idRegister: this.item.registryId,
			});
		},
	},
});
</script>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-cadastros-clients-invites-list', 0);
</style>
