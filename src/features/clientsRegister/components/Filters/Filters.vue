<template>
	<farm-form class="mb-12" ref="form">
		<farm-row>
			<farm-col md="3">
				<farm-label for="form-filtro-status-onboarding">Onboarding</farm-label>
				<farm-select-auto-complete
					id="form-filtro-status-onboarding"
					v-model="filters.status"
					:items="statusOnboardingList"
					item-text="label"
					item-value="id"
				/>
			</farm-col>
			<farm-col md="3">
				<farm-label for="form-filtro-client-type-onboarding">Tipo Pessoa</farm-label>
				<farm-select-auto-complete
					id="form-filtro-client-type-onboarding"
					v-model="filters.tipoPessoa"
					item-text="tipoPessoa"
					item-value="id"
					:items="clientTypeOnboardingList"
				/>
			</farm-col>
		</farm-row>
		<farm-row no-default-gutters>
			<farm-btn outlined class="farm-btn--responsive" title="Aplicar Filtros" @click="apply">
				Aplicar Filtros
			</farm-btn>
			<farm-btn
				plain
				class="farm-btn-responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
				title="Limpar Filtros"
				@click="onFilterClear"
			>
				Limpar Filtros
			</farm-btn>
		</farm-row>
	</farm-form>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		statusOnboardingList: {
			type: Array,
			default: () => [],
		},
		clientTypeOnboardingList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			filters: {},
		};
	},
	methods: {
		apply() {
			this.$emit('onApply', { ...this.filters });
		},
		onFilterClear() {
			this.$refs.form.reset();
			this.filters = {};
			this.$emit('onApply', { ...this.filters });
		},
	},
});
</script>
