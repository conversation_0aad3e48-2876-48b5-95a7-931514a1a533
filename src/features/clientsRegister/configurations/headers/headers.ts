export const headersHome = [
	{
		text: 'ID',
		sortable: false,
		value: 'registryId',
		width: 64,
		align: 'left',
	},
	{
		text: 'ONBOARDING',
		sortable: false,
		value: 'statusOnboarding',
		align: 'center',
		width: 80,
	},
	{
		text: 'CLIENTE',
		sortable: false,
		value: 'name',
		align: 'center',
	},
	{
		text: 'CPF/CNPJ',
		sortable: false,
		value: 'document',
		align: 'center',
		width: 160,
	},
	{
		text: 'TIPO PESSOA',
		sortable: false,
		value: 'type',
		align: 'center',
		width: 160,
	},
	{
		text: 'CLASSIFICAÇÃO',
		sortable: false,
		value: 'rating',
		align: 'center',
		width: 80,
	},
	{
		text: 'ATUALIZAÇÃO',
		sortable: true,
		value: 'lastOnboardUpdate',
		align: 'center',
		width: 176,
	},
	{
		text: '',
		sortable: false,
		value: 'infos',
		align: 'center',
		width: 64,
	},
];

export const documentsHeaders = [
	{
		text: 'Documento',
		sortable: false,
		value: 'fileName',
		align: 'left',
	},
	{
		text: 'E-mail',
		sortable: false,
		value: 'email',
		align: 'center',
	},
	{
		text: 'Tipo',
		sortable: false,
		value: 'documentType',
		align: 'center',
	},
	{
		text: 'Data de Envio',
		sortable: false,
		value: 'createdAt',
		align: 'center',
		width: 96,
	},
	{
		text: '',
		sortable: false,
		value: 'infos',
		align: 'center',
		width: 64,
	},
];

export const inviteHeaders = [
	{
		text: 'Nome',
		sortable: false,
		value: 'name',
		align: 'center',
		width: 320,
	},
	{
		text: 'CPF',
		sortable: false,
		value: 'document',
		align: 'right',
		width: 160,
	},
];

export const modalDocumentsHeaders = [
	{
		text: 'Documento',
		sortable: false,
		value: 'fileName',
		align: 'left',
	},
	{
		text: 'E-mail',
		sortable: false,
		value: 'email',
		align: 'center',
	},
	{
		text: 'Tipo',
		sortable: false,
		value: 'documentType',
		align: 'center',
	},
	{
		text: 'Data de Envio',
		sortable: false,
		value: 'createdAt',
		align: 'center',
		width: 96,
	},
	{
		text: '',
		sortable: false,
		value: 'infos',
		align: 'center',
		width: 64,
	},
];

export const tableInviteHistoricHeaders = [
	{
		text: 'STATUS',
		sortable: false,
		value: 'status',
		align: 'left',
		width: 260,
	},
	{
		text: 'DATA DE MODIFICAÇÃO',
		sortable: false,
		value: 'createdAt',
		align: 'center',
		width: 260,
	},
	{
		text: 'MENSAGEM',
		sortable: false,
		value: 'message',
		align: 'center',
	},
];
