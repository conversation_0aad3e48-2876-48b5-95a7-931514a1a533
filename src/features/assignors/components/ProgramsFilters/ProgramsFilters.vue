<template>
	<farm-box>
		<farm-form v-model="valid">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-status"> Tipo de Programa </farm-label>
					<farm-select-auto-complete
						id="form-filtro-status"
						v-model="filters.type"
						item-text="type"
						item-value="id"
						:items="data"
					/>
				</farm-col>
				<farm-col cols="12" md="9" class="pt-0 pt-sm-8">
					<farm-btn title="Aplicar Filtros" outlined @click="apply">
						Aplicar Filtros
					</farm-btn>
					<farm-btn
						plain
						depressed
						class="ml-0 ml-sm-2 button-top"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			valid: false,
			filters: {
				type: null,
			},
			listItem: [],
		};
	},
	methods: {
		apply(): void {
			this.$emit('onApply', { ...this.filters });
		},
		onFilterClear(): void {
			this.filters = {
				type: null,
			};
			this.$emit('onApply', { ...this.filters });
		},
	},
});
</script>
