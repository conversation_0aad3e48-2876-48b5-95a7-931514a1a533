<template>
	<farm-box>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Programa"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<ProgramsFilters
				v-show="filter"
				key="filters"
				:data="dataFilter"
				@onApply="searchListener"
			/>
		</collapse-transition>
		<ProgramsList v-if="!isError" :data="dataList" />
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import ProgramsFilters from '../ProgramsFilters';
import ProgramsList from '../ProgramsList';

import { sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		ProgramsFilters,
		ProgramsList,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'startDate_DESC',
			sortOptions: sort,
			lastSearchFilters: { page: 0, limit: 10 },
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startDate',
				order: 'DESC',
			},
			filterInputKey: 'name',
			dataFilter: [],
			dataList: [],
			totalPages: 0,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			originatorAssignorPrograms: 'originatorAssignorPrograms',
			originatorAssignorProgramsRequestStatus: 'originatorAssignorProgramsRequestStatus',
			programTypeData: 'programTypeData',
			programTypeDataRequestStatus: 'programTypeDataRequestStatus',
			assignorsDataUser: 'assignorsDataUser',
			assignorsDataUserRequestStatus: 'assignorsDataUserRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.originatorAssignorProgramsRequestStatus,
				this.programTypeDataRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			return this.originatorAssignorProgramsRequestStatus.type === RequestStatusEnum.ERROR;
		},
		isPagination(): boolean {
			return !this.isError && this.dataList.length > 0;
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	mounted() {
		this.doSearch();
		this.getProgramTypes();
	},
	methods: {
		...mapActions('cadastros', {
			getOriginatorAssignorInPrograms: 'getOriginatorAssignorInPrograms',
			getProgramTypes: 'getProgramTypes',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getOriginatorAssignorInPrograms({
				filters: { ...this.filters, ...this.hasSort },
				document: this.currentDocument,
			});
		},
		reload(): void {
			this.doSearch();
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.getOriginatorAssignorInPrograms({
				filters: { ...this.filters, ...this.hasSort },
				document: this.currentDocument,
			});
		},

		updatedHeaderHome(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.generalInfo.acronym,
				listIcons: [
					data.generalInfo.peopleId,
					[data.generalInfo.document, data.generalInfo.raiz],
				],
			});
		},
		updatedList(): void {
			this.dataList = this.originatorAssignorPrograms.content;
		},
		updatedTotalPages(): void {
			this.totalPages = this.originatorAssignorPrograms.totalPages;
		},
		updatedFilter(): void {
			this.dataFilter = this.programTypeData;
		},
	},
	watch: {
		originatorAssignorProgramsRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
				this.updatedTotalPages();
			}
		},
		programTypeDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedFilter();
			}
		},
		assignorsDataUserRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeaderHome(this.assignorsDataUser.content);
			}
		},
	},
});
</script>
