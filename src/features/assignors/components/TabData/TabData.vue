<template>
	<farm-form v-model="valid">
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<farm-collapsible open title="Informações Gerais" icon="clipboard-text">
					<GeneralInfoFormPJ v-model="form.generalInfo" />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12" v-if="!isLoading" class="mt-4">
				<farm-collapsible title="Faturamento" icon="currency-usd">
					<BillingForm v-model="form.billing" />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12" class="mt-4">
				<farm-collapsible title="Endereço" icon="map-marker">
					<AddressForm v-model="form.address" isCompany />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12" class="my-4">
				<farm-collapsible title="Contato Principal" icon="contacts">
					<ContactForm v-model="form.contact" :noValidation="true" />
				</farm-collapsible>
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-form>
</template>
<script lang="ts">
import { mapActions, mapGetters } from 'vuex';
import { defineComponent } from 'vue';
import { RequestStatusEnum, stripTags, notification } from '@farm-investimentos/front-mfe-libs-ts';

import GeneralInfoFormPJ, {
	generalInfoModel,
	GeneralInfoPJTypes,
} from '@/components/GeneralInfoFormPJ';
import AddressForm, { addressModel, AddressTypes } from '@/components/AddressForm';
import ContactForm, { ContactTypes, contactModel } from '@/components/ContactForm';
import BillingForm, { receivedValueTypes, receivedValueModel } from '@/components/BillingForm';

import { createObject } from '@/helpers/createObject';
import { format } from '@/helpers/formatUpdateUser';
import { addMaskInput } from '@/helpers/addMaskInput';
import storage from '@/helpers/storage';
import updateAssignorsFormVariables from '@/helpers/builders/updateAssignorsFormVariables';
import createAssignorsFormPayload from '@/helpers/builders/createAssignorsFormPayload';

export default defineComponent({
	components: {
		GeneralInfoFormPJ,
		BillingForm,
		AddressForm,
		ContactForm,
	},
	props: {
		isEdit: {
			type: Boolean,
			required: true,
		},
	},
	data() {
		return {
			RequestStatusEnum,
			valid: false,
			form: {
				generalInfo: createObject<GeneralInfoPJTypes>(generalInfoModel),
				address: createObject<AddressTypes>(addressModel),
				contact: createObject<ContactTypes>(contactModel),
				billing: createObject<receivedValueTypes>(receivedValueModel),
			},
			labelBtnConfirm: 'Cadastrar',
		};
	},
	computed: {
		...mapGetters('cadastros', {
			documentDataCheck: 'documentDataCheck',
			assignorsDataUser: 'assignorsDataUser',
			assignorsDataUserRequestStatus: 'assignorsDataUserRequestStatus',
			createAssignorsRequestStatus: 'createAssignorsRequestStatus',
			updateAssignorsRequestStatus: 'updateAssignorsRequestStatus',
			checkDocumentRequestStatus: 'checkDocumentRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.assignorsDataUserRequestStatus,
				this.createAssignorsRequestStatus,
				this.updateAssignorsRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			return this.assignorsDataUserRequestStatus.type === RequestStatusEnum.ERROR;
		},
		rules() {
			return { required: value => !!value || 'Campo obrigatório' };
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	mounted(): void {
		this.$emit('onSubmit', this.submit);
		if (this.currentDocument === 'null') {
			notification(RequestStatusEnum.ERROR, 'Originador/cedente, sem documento');
			setTimeout(() => {
				this.$router.push('/admin/cadastros/originadores');
			}, 1000);
			return;
		}
		if (this.isEdit) {
			this.doSearch();
		}
		const document = addMaskInput({
			value: this.currentDocument,
			mask: '##.###.###/####-##',
			quantMin: 14,
		});
		this.form.generalInfo.document = document;
		if (storage.get('branch')) {
			this.doSearch();
		}
	},
	methods: {
		...mapActions('cadastros', {
			getDataAssignors: 'getDataAssignors',
			createAssignor: 'createAssignor',
			updateAssignor: 'updateAssignor',
		}),
		backToList(): void {
			this.$router.push({
				path: `/admin/cadastros/originadores`,
			});
		},
		goToEdit() {
			this.$router.push({
				path: `/admin/cadastros/originadores/${this.currentDocument}/editar?path=dados`,
			});

			this.$router.go(0);
		},
		doSearch(): void {
			this.getDataAssignors({
				document: this.currentDocument,
			});
		},
		reload(): void {
			this.doSearch();
		},
		submit(): void {
			const payload = createAssignorsFormPayload(this.form);
			if (!this.isEdit) {
				this.createAssignor({
					payload,
				});
			}
			if (this.isEdit) {
				this.updateAssignor({
					payload: {
						...payload,
						id: parseInt(this.assignorsDataUser.content.generalInfo.peopleId),
					},
				});
			}
		},
		updatedHeaderHome(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.generalInfo.acronym,
				listIcons: [
					data.generalInfo.peopleId,
					[data.generalInfo.document, data.generalInfo.raiz],
				],
			});
		},
		updateFooterHome(data): void {
			const updatedData = format(data);
			this.$emit('onUpdateFooterFormData', updatedData);
		},
		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.goToEdit();
				})
				.catch(() => {
					this.backToList();
				});
		},
		editDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.doSearch();
				})
				.catch(() => {
					this.backToList();
				});
		},
	},
	watch: {
		createAssignorsRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.createDialog(
					'Originador/Cedente cadastrado com sucesso. Deseja continuar para a edição?'
				);
			} else if (newValue === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, 'criar um originador/cedente');
			}
		},
		updateAssignorsRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.editDialog(
					'Dados do Originador/Cedente atualizados com sucesso. Deseja continuar editando?'
				);
			} else if (newValue === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					'Ocorreu um erro ao editar um originador/cedente.'
				);
			}
		},
		assignorsDataUserRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.form = updateAssignorsFormVariables(this.assignorsDataUser.content);
				this.updatedHeaderHome(this.assignorsDataUser.content);
				this.updateFooterHome(this.assignorsDataUser.meta);
			}
		},
		valid(value): void {
			this.$emit('onDisabledButtonFooter', value);
		},
	},
});
</script>
