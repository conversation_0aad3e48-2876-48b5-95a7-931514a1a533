<template>
    <farm-box>
        <PageForm 
            v-if="!isError" 
            domain="originadores" 
            :type="type" 
            :header="headerConfig"
            :peopleId="peopleId"
        />
        <farm-loader mode="overlay" v-if="isLoading" />
        <div v-if="isError" class="my-10 d-flex justify-center">
            <farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
        </div>
    </farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import PageForm from '@/modules/documents/components/PageForm';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';

import { headersPages } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		PageForm,
	},
	props: {
		type: {
			type: String,
		},
	},
    data() {
		return {
            headerConfig: {},
            peopleId: 0
        };
    },
    computed: {
		...mapGetters('cadastros', {
			originatorAssignorHeaderData:'originatorAssignorHeaderData',
			originatorAssignorHeaderRequestStatus: 'originatorAssignorHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.originatorAssignorHeaderRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.originatorAssignorHeaderRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
    },
    methods: {
        ...mapActions('cadastros', {
			getOriginatorsAssignorsHeader:'getOriginatorsAssignorsHeader',
		}),
        doSearch(): void {
			this.getOriginatorsAssignorsHeader({ document: this.currentDocument });
		},
        reload(): void {
            this.doSearch();
        },
        updatedHeader(): void {
			const data = this.originatorAssignorHeaderData.content;
			this.peopleId = data.id;
			const header = {
				title: data.name,
				listIcons: [data.id, [data.document, data.raiz]],
			};
			this.headerConfig = parseDataHeader(header, headersPages);
		},
    },
    mounted(): void {
        this.doSearch();
	},
    watch: {
        originatorAssignorHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
			}
		},
    }
});
</script>
