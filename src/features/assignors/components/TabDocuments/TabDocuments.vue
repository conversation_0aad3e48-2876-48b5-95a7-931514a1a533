<template>
	<farm-box>
		<PageHome 
			v-if="!isError"
			domain="originadores"
			:peopleId="currentPeopleId"
			@onUpdateDocumentsDataUser="onUpdateDocumentsDataUser"  
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapGetters, mapActions } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import PageHome from '@/modules/documents/components/PageHome';

export default defineComponent({
	components: {
		PageHome,
	},
	data() {
		return {
			currentPeopleId: 0
		};
	},
	computed: {
		...mapGetters('cadastros', {
			originatorAssignorHeaderData:'originatorAssignorHeaderData',
			originatorAssignorHeaderRequestStatus: 'originatorAssignorHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.originatorAssignorHeaderRequestStatus
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [this.originatorAssignorHeaderRequestStatus.type];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getOriginatorsAssignorsHeader:'getOriginatorsAssignorsHeader',
		}),
		doSearch(): void{
			this.getOriginatorsAssignorsHeader({
			document: this.currentDocument
		});
		},
		reload(): void {
			this.doSearch();
		},
		onUpdateDocumentsDataUser(data): void {
			this.$emit('onUpdateFooterFormData', data);
		},
		updatedHeaderHome(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [
					data.id,
					[data.document, data.raiz],
				],
			});
			this.currentPeopleId = data.id;
		},
	},
	watch: {
		originatorAssignorHeaderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeaderHome(this.originatorAssignorHeaderData.content);
			}
		},
	},
});
</script>
