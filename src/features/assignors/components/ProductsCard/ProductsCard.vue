<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
					<CardListTextHeader :data="listIdAndType" noSpacing class="mb-1" />
				</farm-col>
				<farm-col cols="3" align="end">
					<div class="d-flex justify-end align-center">
						<StatusActiveAndInactive :status="data.status" dense class="mr-2" />
						<farm-context-menu
							:items="contextMenuItems(data)"
							@finalizeAssociation="handleFinalizeAssociation(data)"
							@reAssociate="handleReAssociateOption(data)"
							@edit="handleEdit(data)"
						/>
					</div>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row class="mb-4">
				<farm-col cols="6">
					<CardTextBody label="Carteira" :value="formatValueOrNA(data.wallet)" />
				</farm-col>

				<farm-col cols="6">
					<CardTextBody
						label="Percentual de Concentração"
						:value="formatConcentrationPercentage(data.concentrationPercentage)"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col cols="6">
					<CardTextBody
						label="Início de Associação"
						:value="formatDateOrNA(data.startDate)"
					/>
				</farm-col>
				<farm-col cols="6">
					<CardTextBody label="Fim de Associação" :value="formatDateOrNA(data.endDate)" />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import { formatDateOrNA, formatValueOrNA } from '@/helpers/formatCards';
import { parseConcentrationPercentage as formatConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatDateOrNA,
			formatConcentrationPercentage,
			formatValueOrNA,
			listIdAndType: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{ label: 'Tipo', value: this.data.type, copyText: '' },
			],

			terminateAssociationOption: {
				label: 'Finalizar associação',
				handler: 'finalizeAssociation',
				icon: { color: 'error', type: 'close-circle-outline' },
			},
			edit: {
				label: 'Editar',
				handler: 'edit',
				icon: { color: 'primary', type: 'pencil-outline' },
			},
			reAssociate: {
				label: 'Associar Novamente',
				handler: 'reAssociate',
				icon: { color: 'primary', type: 'reload' },
			},
		};
	},
	methods: {
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			if (this.data.endDate === null) {
				return [this.edit, this.terminateAssociationOption];
			} else {
				return [this.reAssociate];
			}
		},
		handleEdit(item): void {
			this.$emit('handleEdit', item);
		},
		handleFinalizeAssociation(item): void {
			this.$emit('handleFinalizeAssociation', item);
		},
		handleReAssociateOption(item): void {
			this.$emit('handleReAssociateOption', item);
		},
	},
});
</script>
