<template>
	<farm-modal v-model="inputVal" size="sm" :offsetTop="48" :offsetBottom="68">
		<template v-slot:header>
			<farm-dialog-header :title="title" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-form v-model="valid" ref="form" class="mt-3">
				<farm-row>
					<farm-col cols="12">
						<farm-label :required="true">
							{{ label }}
						</farm-label>
						<farm-textfield-v2
							v-mask="'##############'"
							v-model="document"
							maxlength="14"
							:rules="[rules.required, rules.validate]"
						/>
					</farm-col>
				</farm-row>
				<farm-loader mode="overlay" v-if="loading" />
			</farm-form>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Continuar"
				:isConfirmDisabled="!valid"
				@onConfirm="confirm"
				@onClose="close"
			/>
		</template>
	</farm-modal>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { isValidCNPJ, isValidCPF } from '@farm-investimentos/front-mfe-libs-ts';

import { cleanDocument } from '@/helpers/masks';

export default defineComponent({
	props: {
		value: {
			required: true,
		},
		loading: {
			type: Boolean,
			default: false,
		},
		hasPF: {
			type: Boolean,
			default: false,
		},
		label: {
			type: String,
			default: 'Digite o CNPJ',
		},
		title: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			valid: null,
			document: null,
			rules: {
				required: value => {
					return !!value || 'Campo obrigatório';
				},
				validate: value => {
					const dataValue = cleanDocument(value);
					if (this.hasPF && dataValue.length <= 11) {
						return isValidCPF(dataValue) ? true : 'CPF inválido';
					}
					if (dataValue.length > 12) {
						return isValidCNPJ(dataValue) ? true : 'CNPJ inválido';
					}
					return this.hasPF === false ? 'CNPJ inválido' : false;
				},
			},
		};
	},
	computed: {
		inputVal: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			},
		},
	},
	methods: {
		close() {
			this.inputVal = false;
		},
		confirm() {
			this.$emit('onContinues', cleanDocument(this.document));
		},
	},
	watch: {
		inputVal(newValue) {
			if (!newValue) {
				this.document = '';
			}
		},
	},
});
</script>
