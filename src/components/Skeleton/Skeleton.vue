<template>
	<div
		:class="{
			skeleton: true,
			circle: circle,
		}"
		:style="{
			width: width,
			height: height,
			'margin-top': top,
			'margin-bottom': bottom,
			'margin-left': left,
			'margin-right': right,
			'border-radius': radius,
		}"
	></div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'skeleton',
	props: {
		width: {
			type: String,
			default: '100%',
		},
		height: {
			type: String,
			default: '20px',
		},
		circle: {
			type: Boolean,
			default: false,
		},
		top: {
			type: String,
			default: '0px',
		},
		bottom: {
			type: String,
			default: '0px',
		},
		left: {
			type: String,
			default: '0px',
		},
		right: {
			type: String,
			default: '0px',
		},
		radius: {
			type: String,
			default: '5px',
		},
	},
});
</script>

<style lang="scss" scoped>
@import './Skeleton';
</style>
