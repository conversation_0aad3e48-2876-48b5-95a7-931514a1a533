<template>
	<farm-card>
		<farm-card-content gutter="md">
			<farm-row class="mb-3">
				<farm-col cols="6">
					<farm-caption variation="regular"> Nome </farm-caption>
					<farm-bodytext :type="2" variation="bold">{{ data.name }}</farm-bodytext>
				</farm-col>
				<farm-col cols="5">
					<farm-caption variation="regular"> CNPJ Associado </farm-caption>
					<farm-bodytext :type="2" variation="bold">{{
						formattedDocument
					}}</farm-bodytext>
				</farm-col>
				<farm-col cols="1">
					<TableContextMenu
						:items="contextMenuItems()"
						@edit="handleEdit(data)"
						@remove="handleRemove(data)"
					/>
				</farm-col>
			</farm-row>
			<farm-row v-if="data.type !== 'account'">
				<farm-col cols="6">
					<farm-caption variation="regular"> Data de cadastro </farm-caption>
					<farm-bodytext :type="2" variation="bold">{{
						defaultDateFormat(data.createdAt)
					}}</farm-bodytext>
				</farm-col>
				<farm-col cols="5">
					<farm-caption variation="regular"> Tipo </farm-caption>
					<farm-bodytext :type="2" variation="bold">{{ data.keyType }}</farm-bodytext>
				</farm-col>
			</farm-row>
			<farm-row v-if="data.type !== 'account'" class="mt-3">
				<farm-col cols="11">
					<farm-caption variation="regular"> Chave PIX </farm-caption>
					<farm-bodytext :type="2">{{ data.key }}</farm-bodytext>
				</farm-col>
			</farm-row>
			<farm-row v-if="data.type === 'account'" class="mt-3">
				<farm-col cols="3">
					<farm-caption variation="regular"> Data de cadastro </farm-caption>
					<farm-bodytext :type="2" variation="bold">{{
						defaultDateFormat(data.createdAt)
					}}</farm-bodytext>
				</farm-col>
				<farm-col cols="5">
					<farm-caption variation="regular"> Banco </farm-caption>
					<farm-bodytext :type="2" variation="bold">{{ data.bank }}</farm-bodytext>
				</farm-col>
				<farm-col cols="4">
					<farm-caption variation="regular"> Tipo de conta </farm-caption>
					<farm-bodytext :type="2" variation="bold">{{ data.accountType }}</farm-bodytext>
				</farm-col>

				<farm-col cols="3" class="mt-3">
					<farm-caption variation="regular"> Conta </farm-caption>
					<farm-bodytext :type="2">{{ data.account }}</farm-bodytext>
				</farm-col>
				<farm-col cols="5" class="mt-3">
					<farm-caption variation="regular"> Agência </farm-caption>
					<farm-bodytext :type="2">{{ data.agency }}</farm-bodytext>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import {
	edit as editOption,
	remove as removeOption,
	defaultDateFormat,
} from '@farm-investimentos/front-mfe-libs-ts';

import storage from '@/helpers/storage';

export default defineComponent({
	props: {
		data: {
			type: Object,
		},
	},
	data() {
		return {
			defaultDateFormat,
		};
	},
	computed: {
		currentDocument(): string {
			return this.$route.params.document;
		},
		formattedDocument(): String {
			let d = this.data.document;
			if (d) {
				return this.data.document.replace(
					/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
					'$1.$2.$3/$4-$5'
				);
			}
			return '';
		},
	},
	methods: {
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			return [editOption, removeOption];
		},
		handleEdit(item): void {
			const document = this.currentDocument;
			storage.set('typeAccount', item.type);
			this.$router.push({
				path: `/admin/cadastros/fornecedores/v2/${document}/dados_bancarios/${item.id}/editar`,
			});
		},
		handleRemove(item): void {
			this.$emit('removeDataBank', item);
		},
	},
});
</script>

<style lang="scss" scoped>
@import './CardBank';
</style>
