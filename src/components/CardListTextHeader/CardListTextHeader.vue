<template>
	<div
		:class="{
			'd-flex': data.length > 1,
			'flex-column': flexColumn,
		}"
	>
		<farm-idcaption
			v-for="(item, index) in data"
			tooltipColor="gray"
			:key="item.id"
			:copyText="item.copyText"
			:noHeight="noSpacing"
			:successMessage="item.successMessage"
			:class="{
				'with-line': index !== data.length - 1,
				'with-ellipsis': item.ellipsis,
			}"
		>
			<template v-slot:subtitle>
				{{ hasLabel(item) }}
				<farm-caption
					color="neutral"
					color-variation="darken"
					tag="span"
					:ellipsis="item.ellipsis"
					:title="item.value"
				>
					{{ item.value || 'N/A' }}
				</farm-caption>
			</template>
		</farm-idcaption>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { CardListTextType } from './types';

export default defineComponent({
	props: {
		data: {
			type: Array as PropType<Array<CardListTextType>>,
			required: true,
		},
		noSpacing: {
			type: Boolean,
			default: false,
		},
		flexColumn: {
			type: Boolean,
			default: false,
		},
	},
	methods: {
		hasLabel(item) {
			return item.label ? `${item.label}: ` : '';
		},
	},
});
</script>

<style lang="scss" scoped>
@import './CardListTextHeader';
</style>
