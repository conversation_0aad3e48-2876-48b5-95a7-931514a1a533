<template>
	<farm-chip :dense="dense" :color="color" variation="lighten">{{ label }}</farm-chip>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

import { HISTORIC_STATUS } from '@/constants';

export default defineComponent({
	props: {
		status: {
			type: Number,
		},
		dense: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			color: HISTORIC_STATUS[this.status].color,
			label: HISTORIC_STATUS[this.status].label,
		};
	},
});
</script>
