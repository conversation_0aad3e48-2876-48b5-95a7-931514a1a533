<template>
	<farm-row>
		<farm-col cols="12" md="4">
			<farm-label for="form-contact-name"> Nome do Contato </farm-label>
			<farm-textfield-v2 v-model="value.contactName" id="form-contact-name" />
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-contact-phone"> Telefone </farm-label>
			<farm-textfield-v2
				v-model="value.phoneNumber"
				mask="(##) ####-####"
				id="form-contact-phone"
				:rules="[rules.isValidPhone]"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-contact-cell">
				Celular / Whatsapp <span v-if="!isFromFormPJ && !noValidation" class="required">*</span>
			</farm-label>
			<farm-textfield-v2
				v-model="value.cellphoneNumber"
				:mask="cellphoneMask"
				id="form-contact-cell"
				:rules="[rules.isValidCellphone]"
				persistent-hint
				@input="handleCellphoneInput"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-contact-email"> E-mail </farm-label>
			<farm-textfield-v2
				v-model="value.email"
				type="email"
				id="form-contact-email"
				:rules="[rules.email]"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-contact-site"> Site </farm-label>
			<farm-textfield-v2 v-model="value.site" type="url" id="form-contact-site" />
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { email as emailValidator } from '@farm-investimentos/front-mfe-libs-ts';
import { ContactTypes } from './types';
import { removeMask } from '@/helpers/removeMask';

export default defineComponent({
	props: {
		value: {
			type: Object as PropType<ContactTypes>,
			required: true,
		},
		isFromFormPJ: {
			type: Boolean,
			default: false,
		},
		noValidation: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			cellphoneMask: '(##) # ####-####',
			rules: {
				email: value => {
					if (!value) return true;
					return emailValidator(value) || 'E-mail inválido';
				},

				isValidCellphone: value => {
					if (!value) {
						if (this.isFromFormPJ || this.noValidation) return true;
						return 'Campo obrigatório';
					}

					const valueClean = removeMask(value);
					const ddd = valueClean.slice(0, 2);
					const firstDigitAfterDDD = valueClean.charAt(2);

					if (![10, 11].includes(valueClean.length)) return 'Formato inválido';
					if (ddd.startsWith('0')) return 'DDD inválido';

					if (valueClean.length === 11 && firstDigitAfterDDD !== '9') {
						return 'O número de celular deve iniciar com 9';
					}

					if (firstDigitAfterDDD === '9' && valueClean.length !== 11) {
						return 'Formato inválido';
					}

					if (valueClean.length === 11 && firstDigitAfterDDD === '9') {
						return true;
					}

					if (valueClean.length === 10) {
						return true;
					}

					return 'Formato inválido';
				},

				isValidPhone: value => {
					if (this.noValidation) return true;

					if (!value) return true;
					const valueClean = removeMask(value);
					return valueClean.length === 10 || 'Formato inválido';
				},
			},
		};
	},
	computed: {
		isFormValid() {
			if (this.noValidation) return true;

			return (
				!!this.value.cellphoneNumber &&
				this.rules.isValidCellphone(this.value.cellphoneNumber) === true
			);
		},
	},
	methods: {
		handleCellphoneInput(value: string) {
			const numeric = removeMask(value);

			if (numeric.length === 10 && !numeric.slice(2).startsWith('9')) {
				this.cellphoneMask = '(##) ####-####';
			} else if (numeric.length === 11 && numeric.slice(2).startsWith('9')) {
				this.cellphoneMask = '(##) # ####-####';
			} else {
				this.cellphoneMask = '(##) # ####-####';
			}
		},
	},
});
</script>

<style scoped>
.required {
	color: red;
}
</style>
