<template>
	<farm-contextmenu v-model="value" bottom>
		<template v-slot:activator>
			<farm-btn
				title="Importar"
				:color="color ? color : 'secondary'"
				class="d-flex align-center justify-center"
				:outlined="outlined"
			>
				{{ label }}
				<farm-icon v-if="icon" color="primary" class="ml-1" size="md">
					{{ !value ?'chevron-down': 'chevron-up' }}
				</farm-icon>
			</farm-btn>
		</template>
		<farm-list>
			<farm-listitem
				v-for="item in options"
				clickable
				hoverColor="primary"
				hoverColorVariation="lighten"
				:key="'importbutton_key_' + item.title"
				:title="item.title"
				@click="onClick(item.link)"
			>
				<farm-icon color="primary" size="sm" class="farm-icon"> {{ item.icon }} </farm-icon>
				<farm-caption bold tag="span" class="farm-text">{{ item.title }}</farm-caption>
			</farm-listitem>
		</farm-list>
	</farm-contextmenu>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { ButtonImportTypes } from './types';

export default defineComponent({
	props: {
		options: {
			type: Array as PropType<Array<ButtonImportTypes>>,
			default: () => [],
		},
		label: {
			type: String,
			default: 'Importações',
		},
		outlined: {
			type: Boolean,
		},
		color: {
			type: String,
		},
		icon: {
			type: Boolean,
		},
	},
	data() {
		return {
			value: false,
		};
	},
	computed: {
		inputVal: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			},
		},
	},
	methods: {
		onClick(key: string): void {
			this.$emit('onClick', key);
		},
	},
});
</script>
<style lang="scss" scoped>
@import './ButtonImport';
</style>
