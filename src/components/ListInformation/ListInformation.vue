<template>
	<div class="list-information">
		<farm-idcaption
			tooltipColor="gray"
			v-for="(item, index) in data"
			:key="`list-information-${item.id}-${index}`"
			:icon="item.icon"
			:copyText="item.copyText"
			:class="{
				'list-information-item': true,
				'with-line': index !== data.length - 1,
			}"
			:successMessage="messageSucess"
		>
			<template v-slot:title v-if="item.title">
				{{ item.title || 'Carregando...' }}
			</template>
			<template v-slot:subtitle>
				{{ item.subtitle }}: {{ item.value || 'Carregando...' }}
			</template>
		</farm-idcaption>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { ListInformationTypes } from './types';

export default defineComponent({
	props: {
		data: {
			type: Array as PropType<Array<ListInformationTypes>>,
			default: () => [],
		},
		messageSucess: {
			type: String,
			default: 'Raiz copiada para área de transferência!',
		},
	},
	computed: {
		hasSuccessMessage() {},
	},
});
</script>

<style lang="scss" scoped>
@import './ListInformation';
</style>
