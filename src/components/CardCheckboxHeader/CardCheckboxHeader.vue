<template>
	<div class="d-flex justify-space-between">
		<div class="d-flex align-center check-box-content">
			<slot name="checkbox"></slot>
			<div class="title-and-list-content">
				<slot name="title"></slot>
			</div>
		</div>
		<div class="status-content">
			<slot name="status"></slot>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({});
</script>

<style lang="scss" scoped>
@import './CardCheckboxHeader';
</style>
