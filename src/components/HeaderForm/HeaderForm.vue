<template>
	<farm-box class="mb-6">
		<farm-row>
			<farm-col cols="12">
				<farm-heading :type="6" class="mb-4">
					{{ data.title || 'Carregando...' }}
				</farm-heading>
			</farm-col>
			<farm-col cols="12">
				<ListInformation
					class="mb-4"
					:data="data.dataList"
					:messageSucess="data.messageSucess"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="withLine">
			<farm-line noSpacing />
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import ListInformation from '@/components/ListInformation';

import { HeaderFormTypes } from './types';

export default defineComponent({
	components: {
		ListInformation,
	},
	props: {
		data: {
			type: Object as PropType<HeaderFormTypes>,
			default: () => ({}),
		},
		withLine: {
			type: <PERSON>olean,
			default: () => false,
		},
	},
});
</script>
