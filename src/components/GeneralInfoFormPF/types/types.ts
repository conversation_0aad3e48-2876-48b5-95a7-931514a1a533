export type GeneralInfoTypes = {
	document: string;
	fullName: string;
	socialName: string;
	rg: string;
	peopleNationalityId: string | number;
	birthDate: string;
	birthplace: string;
	peopleGenderId: string | number;
	peopleMaritalStatusId: string | number;
	professionId: string | number;
	startRelationship: string;
	stateRegistration: string;
	ruralRegistration: string;
	cdcaAble: true;
	pep: boolean;
	status: boolean;
	rgUfStateRegistration: string | number;
	registrationStatus: string;
	estimatedMonthlyIncome: string | number;
	registrationStatusDate: string;
	membershipTerm: string;
};

export const generalInfoModel: GeneralInfoTypes = {
	document: null,
	fullName: null,
	socialName: null,
	cdcaAble: null,
	rg: null,
	peopleNationalityId: '',
	birthDate: '',
	birthplace: '',
	peopleGenderId: '',
	peopleMaritalStatusId: '',
	professionId: '',
	startRelationship: '',
	stateRegistration: null,
	ruralRegistration: null,
	pep: false,
	status: false,
	rgUfStateRegistration: '',
	registrationStatus: null,
	estimatedMonthlyIncome: '',
	registrationStatusDate: '',
	membershipTerm: null,
};
