<template>
	<farm-modal v-model="inputVal" :offsetTop="48" :offsetBottom="48">
		<template v-slot:header>
			<farm-dialog-header :title="title" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-row class="mt-3">
				<farm-col cols="12">
					<farm-label>
						{{ label }}
					</farm-label>
				</farm-col>
			</farm-row>
			<farm-row class="my-3">
				<farm-col cols="12">
					<farm-caption
						class="mb-3"
						variation="semiBold"
						color="black"
						color-variation="50"
					>
						Deseja exportar Sacados sem associação com Produto?
					</farm-caption>
					<farm-radio-group v-model="hasAssociation">
						<div class="d-flex">
							<div class="d-flex">
								<div class="mr-3">
									<farm-radio v-model="hasAssociation" value="1" size="md" />
								</div>
								<div class="mr-3">
									<farm-bodytext color="black" color-variation="50" :type="2">
										Não
									</farm-bodytext>
								</div>
								<div class="mr-3">
									<farm-radio v-model="hasAssociation" value="0" size="md" />
								</div>
								<div>
									<farm-bodytext color="black" color-variation="50" :type="2">
										Sim
									</farm-bodytext>
								</div>
							</div>
						</div>
					</farm-radio-group>
				</farm-col>
			</farm-row>
			<farm-row align="center">
				<farm-col cols="6" class="d-flex justify-start">
					<LabelRequestResults
						:totalItems="selectedProductTable.length"
						type="selecteds"
					/>
				</farm-col>
				<farm-col cols="6" class="d-flex justify-end">
					<farm-btn-confirm
						title="Desmarcar Selecionados"
						outlined
						:disabled="selectedProductTable.length === 0"
						@click="removeAllSelectedItemTable"
					>
						Desmarcar Selecionados
					</farm-btn-confirm>
				</farm-col>
			</farm-row>
			<farm-row class="mt-3">
				<farm-col cols="12">
					<v-data-table
						id="table-drawees-export-list"
						hide-default-footer
						show-select
						v-model="selectedProductTable"
						:items="products"
						:server-items-length="products.length"
						:hide-default-header="showCustomHeader()"
						:headers="headers"
					>
						<template v-slot:header="{ props, on }" v-if="showCustomHeader()">
							<farm-datatable-header
								v-model="props.everyItem"
								:headers="props.headers"
								:sortClick="sortClicked"
								:headerProps="props"
								@onClickSort="onSort"
								@toggleSelectAll="on['toggle-select-all']"
							/>
						</template>

						<template slot="no-data">
							<farm-emptywrapper title="Nenhum produto cadastrado." />
						</template>
					</v-data-table>
					<farm-datatable-paginator
						class="mx-0 mt-6"
						:page="currentPage"
						:totalPages="totalPages"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangeLimitPerPage"
					/>
				</farm-col>
			</farm-row>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Exportar"
				closeLabel="Cancelar"
				:isConfirmDisabled="!isConfirmDisabled"
				@onConfirm="confirm"
				@onClose="close"
			/>
		</template>
		<farm-loader mode="overlay" v-if="false" />
	</farm-modal>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	pageable,
	exportHandler,
	RequestStatusEnum,
	unFormatDate,
} from '@farm-investimentos/front-mfe-libs-ts';

import LabelRequestResults from '../LabelRequestResults';
import { modalHeaders } from './configurations/headers';

export default defineComponent({
	mixins: [pageable, exportHandler],
	components: {
		LabelRequestResults,
	},
	props: {
		value: {
			required: true,
		},
		label: {
			type: String,
			required: true,
		},
		title: {
			type: String,
			required: true,
		},
		createdAtRange: {
			type: Object,
			required: true,
		},
		updatedAtRange: {
			type: Object,
			required: true,
		},
		exportType: {
			type: String,
			required: true,
		},
		filtersExport: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headers: modalHeaders,
			products: [],
			selectedProductTable: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			lastSearchFilters: {},
			totalPages: 0,
			datesParams: {},
			hasAssociation: '1',
		};
	},
	mounted(): void {
		this.doSearch();
	},
	computed: {
		...mapGetters('cadastros', {
			exportDraweeByRangeDraweeRequestStatus: 'exportDraweeByRangeDraweeRequestStatus',
			associatedProductsToDraweeData: 'associatedProductsToDraweeData',
			associatedProductsToDraweeRequestStatus: 'associatedProductsToDraweeRequestStatus',
		}),
		inputVal: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			},
		},
		isLoading(): boolean {
			const requestStatus = [
				this.associatedProductsToDraweeRequestStatus,
				this.exportDraweeByRangeDraweeRequestStatus,
			];

			return requestStatus.includes(RequestStatusEnum.START);
		},
		isConfirmDisabled(): boolean {
			return this.selectedProductTable.length > 0 || parseInt(this.hasAssociation, 10) === 0;
		},
	},
	methods: {
		...mapActions('cadastros', {
			exportDraweeByRange: 'exportDraweeByRange',
			getAssociatedProductsToDrawee: 'getAssociatedProductsToDrawee',
		}),
		doSearch(): void {
			this.getDatesBiggerAndLower();
			this.lastSearchFilters = { ...this.filters };
			const params = {
				...this.filters,
				...this.hasSort,
				...this.datesParams,
			};
			this.getAssociatedProductsToDrawee({
				filters: params,
			});
		},
		createFilter(data) {
			let newFilter = { ...data };
			if (this.filtersExport.status !== null) {
				newFilter = {
					...newFilter,
					status: this.filtersExport.status,
				};
			}
			if (this.filtersExport.type !== null) {
				newFilter = {
					...newFilter,
					type: this.filtersExport.type,
				};
			}
			return newFilter;
		},
		close(): void {
			this.inputVal = false;
		},
		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		removeAllSelectedItemTable() {
			this.selectedProductTable = [];
		},
		confirm(): void {
			const productIds = this.selectedProductTable.map(p => p.id);
			const exportType = this.exportType.toLowerCase();
			const params = this.createFilter({
				...this.datesParams,
				productIds: productIds,
				exportType,
				hasAssociation: this.hasAssociation,
			});
			const callback = () => {
				this.exportDraweeByRange({
					filters: params,
				});
			};
			this.exportHandler(callback);
		},
		getDatesBiggerAndLower(): void {
			const { lowest: createdAtRangeInicio, biggest: createdAtRangeFim } =
				this.createdAtRange;
			const { lowest: updatedAtRangeInicio, biggest: updatedAtRangeFim } =
				this.updatedAtRange;

			this.datesParams = {
				createdAtRangeInicio: unFormatDate(createdAtRangeInicio),
				createdAtRangeFim: unFormatDate(createdAtRangeFim),
				updatedAtRangeInicio: unFormatDate(updatedAtRangeInicio),
				updatedAtRangeFim: unFormatDate(updatedAtRangeFim),
			};
		},
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const parseOrderby = {
				name: 'name',
				type: 'type',
			};
			const filtersActive = {
				...this.filterCurrent,
				orderby: parseOrderby[data.field],
				order: data.descending,
			};
			this.doSearch({ ...filtersActive });
		},
		onChangePage(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.filters.page = pageActive;
			this.doSearch({ ...this.filters, page: pageActive });
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.filters.limit = limit;
			this.doSearch({ ...this.filters, page: 0, limit: limit });
		},
	},
	watch: {
		associatedProductsToDraweeRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.products = this.associatedProductsToDraweeData.content;
				this.totalPages = this.associatedProductsToDraweeData.totalPages;
			}
		},
	},
});
</script>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-drawees-export-list', 1, (0));
</style>

<style lang="scss" scoped>
@import './ModalExport.scss';
</style>
