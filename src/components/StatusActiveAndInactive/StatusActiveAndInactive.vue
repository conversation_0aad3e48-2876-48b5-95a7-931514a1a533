<template>
	<farm-chip :color="color" :dense="dense" v-if="formateText">
		{{ formateText || 'N/A' }}
	</farm-chip>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	name:'status-active-and-inactive',
	props: {
		status: {
			type: String,
			require: true,
		},
		dense: {
			type: Boolean,
			default: false,
		},
		uppercase: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		color() {
			if (this.status) {
				return this.status.toUpperCase() === 'ATIVO' ? 'success' : 'neutral';
			}
		},
		formateText() {
			if (!this.status) {
				return null;
			}
			if (this.uppercase) {
				return this.status.toUpperCase();
			}
			const text = this.status.toLowerCase();
			return `${text[0].toUpperCase()}${text.slice(1)}`;
		},
	},
});
</script>
